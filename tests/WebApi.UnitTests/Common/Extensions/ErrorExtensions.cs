using System.Text.Json;
using FastEndpoints;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using PaymentGateway.WebApi.Common;
using ProblemDetails = Microsoft.AspNetCore.Mvc.ProblemDetails;

namespace WebApi.UnitTests.Common.Extensions;

public static class ErrorExtensions
{
    public static async Task AssertBadRequest<TRequest, TResponse>(this Endpoint<TRequest, TResponse> endpoint, Error expectedError) where TRequest : notnull
    {
        if (endpoint.HttpContext.Response.StatusCode != StatusCodes.Status400BadRequest) 
            throw new Exception("AssertBadRequest can only be used for 400 responses.");

        endpoint.HttpContext.Response.Body.Seek(0, SeekOrigin.Begin);

        using var reader = new StreamReader(endpoint.HttpContext.Response.Body, leaveOpen: true);
        var responseBody = await reader.ReadToEndAsync();

        endpoint.HttpContext.Response.Body.Seek(0, SeekOrigin.Begin);

        var problem = JsonSerializer.Deserialize<ProblemDetails>(responseBody, new JsonSerializerOptions(JsonSerializerDefaults.Web));

        problem.Should()
            .NotBeNull("we expect a ProblemDetails response");


        problem!.Title
            .Should()
            .Be(expectedError.Code);
        problem.Detail
            .Should()
            .Be(expectedError.Message);
        problem.Type
            .Should()
            .Be(expectedError.Number.ToString());
        problem.Status
            .Should()
            .Be(expectedError.StatusCode);
    }


    public static void AssetErrorResponse<TRequest, TResponse>(this Endpoint<TRequest, TResponse> endpoint, int statusCode) where TRequest : notnull
    {
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(statusCode);
        endpoint.HttpContext
            .Response
            .Body
            .Should()
            .NotBeNull();
    }
}
