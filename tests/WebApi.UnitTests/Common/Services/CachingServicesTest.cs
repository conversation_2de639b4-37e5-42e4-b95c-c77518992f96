using Microsoft.Extensions.Caching.Memory;
using Moq;
using PaymentGateway.WebApi.Common.Configurations;
using PaymentGateway.WebApi.Common.Services;

namespace WebApi.UnitTests.Common.Services;

public class CachingServicesTest
{
    private readonly Mock<ICacheEntry> _cacheEntryMock = new();
    private readonly CachingConfiguration _cachingConfiguration = new() { ExpirationInSecond = 60, AbsoluteExpiration = 90, };
    private readonly Mock<IMemoryCache> _memoryCacheMock = new();

    [Fact]
    public async Task SetCache_ShouldStoreValueInMemoryCache()
    {
        // Arrange
        var key = "TestKey";
        var value = "TestValue";
        var service = SetupService();

        _memoryCacheMock.Setup(m => m.CreateEntry(key))
            .Returns(_cacheEntryMock.Object);

        // Act
        await service.SetAsync(key, value);

        // Assert
        _memoryCacheMock.Verify(m => m.CreateEntry(key), Times.Once);
        _cacheEntryMock.VerifySet(entry => entry.Value = value, Times.Once);
        _cacheEntryMock.VerifySet(entry => entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(_cachingConfiguration.AbsoluteExpiration), Times.Once);
        _cacheEntryMock.VerifySet(entry => entry.SlidingExpiration = TimeSpan.FromSeconds(_cachingConfiguration.ExpirationInSecond), Times.Once);
        _cacheEntryMock.Verify(entry => entry.Dispose(), Times.Once);
    }

    private MemoryCachingService SetupService() => new(_cachingConfiguration, _memoryCacheMock.Object);
}
