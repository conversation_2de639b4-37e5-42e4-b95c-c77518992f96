using FluentValidation.TestHelper;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Orders.OrderUiRegister;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace WebApi.UnitTests.Orders.OrderUiRegister;

public class OrderUiRegisterRequestValidatorTest
{
    private readonly OrderUiRegisterRequestValidator _validator = new();
    
    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void Validator_ShouldHaveError_WhenAmountIsLessThanOrEqualZero(int amount)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: amount, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: null, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com"
        );

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Amount)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderAmountMustBeGreaterThanZero.ToString());
    }

    [Theory]
    [InlineData(1)]
    [InlineData(12)]
    public void Validator_ShouldNotHaveError_WhenAmountIsGreaterThanZero(int amount)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: amount, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: null, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Amount);
    }

    [Theory]
    [InlineData(CurrencyIso4217.IQD)]
    [InlineData(CurrencyIso4217.USD)]
    public void Validator_ShouldNotHaveError_WhenCurrencyIsValid(CurrencyIso4217 currency)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: currency, CustomerId: null, Phone: null, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Currency);
    }

    [Fact]
    public void Validator_ShouldHaveError_WhenCurrencyIsInvalid()
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: (CurrencyIso4217)4, // Invalid
            CustomerId: null, Phone: null, Email: null, ExpiresInSeconds: 600, StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Currency)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderCurrencyMustBeValid.ToString());
    }

    [Theory]
    [InlineData("07512345678")]
    [InlineData("07712345678")]
    [InlineData("07812345678")]
    [InlineData("07912345678")]
    [InlineData("")]
    public void Validator_ShouldNotHaveError_WhenValidPhoneNumberOrEmpty(string phone)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: phone, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Phone);
    }

    [Theory]
    [InlineData("0751234567")]
    [InlineData("0771234567")]
    [InlineData("0781234567")]
    [InlineData("0791234567")]
    [InlineData("07312345678")]
    public void Validator_ShouldHaveError_WhenInvalidPhoneBasedOnRegex(string phone)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: phone, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Phone)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderPhoneMustBeStartWith07.ToString());
    }

    [Theory]
    [InlineData("x @x.com")]
    [InlineData("<EMAIL>")]
    [InlineData(".@x.com")]
    [InlineData("x@x")]
    [InlineData("x@x.")]
    [InlineData("x@x.x")]
    [InlineData("x@x.x.")]
    [InlineData("x@x.x.x")]
    [InlineData("x@x.x.x.")]
    [InlineData("x@x_")]
    [InlineData("x@_.com")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("ق@_.com")]
    [InlineData("ي@s.com")]
    [InlineData(" ")]
    [InlineData("")]
    public void Validator_ShouldHaveError_WhenEmailIsInvalid(string email)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: null, Email: email, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Email)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderEmailMustBeValid.ToString());
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData(null)]
    [InlineData("<EMAIL>")]
    public void Validator_ShouldNotHaveError_WhenEmailIsValidOrNull(string? email)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: null, Email: email, ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://validurl.com");

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Email);
    }

    // -- Unique validation tests for StatusRedirectUrl --

    [Theory]
    [InlineData("http://valid.com")]
    [InlineData("http://google.com")]
    [InlineData("https://valid.com")]
    [InlineData("https://valid.iq")]
    [InlineData("https://valid.org")]
    [InlineData("http://www.google.net")]
    [InlineData("https://www.google.net")]
    [InlineData("https://pay.google.net")]
    [InlineData("https://pay2.google.net")]
    [InlineData("https://10.5.5.5")]
    [InlineData("http://10.5.5.5")]
    public void Validator_ShouldNotHaveError_WhenStatusRedirectUrlIsValid(string statusRedirectUrl)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: null, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: statusRedirectUrl);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.StatusRedirectUrl);
    }

    [Theory]
    [InlineData("")]
    [InlineData("invalid")]
    [InlineData(null)]
    [InlineData("http//google.com")]
    [InlineData("http://google")]
    [InlineData("http:/google.com")]
    [InlineData("http:// google.com")]
    [InlineData("htt://google.com")]
    [InlineData("http://.google.com")]
    [InlineData("http://10.5.5")]
    [InlineData("http://10.5.5.5.")]
    [InlineData("http://10.5.5.5.0")]
    [InlineData("10.5.5.5")]
    public void Validator_ShouldHaveError_WhenStatusRedirectUrlIsInvalid(string? statusRedirectUrl)
    {
        // Arrange
        var request = new OrderUiRegisterRequest(ExternalId: null, Amount: 10, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: null, Email: null, ExpiresInSeconds: 600,
            StatusRedirectUrl: statusRedirectUrl);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.StatusRedirectUrl)
            .WithErrorMessage(OrderErrors.OrderStatusRedirectUrlMustBeValid.Message)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderStatusRedirectUrlMustBeValid.ToString());
    }
}
