using FastEndpoints;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Configurations;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderUiRegister;
using PaymentGateway.WebApi.Features.Orders.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace WebApi.UnitTests.Orders.OrderUiRegister;

public class OrderUiRegisterEndpointFeatureFlagTests
{
    private readonly Mock<IClickhouseTaskBuilder> _mockClickhouseTaskBuilder = new();
    private readonly Mock<IRequestIdResolver> _mockRequestIdResolver = new();
    private readonly Mock<IOrderUpserter> _mockOrderUpserter = new();
    private readonly Mock<IIpResolver> _mockIpResolver = new();
    private readonly PaymentDbContext _mockContext = null!;
    private readonly Mock<ILogger<OrderUiRegisterEndpoint>> _mockLogger = new();
    private readonly Mock<IOrderRegistrationService> _mockOrderRegistrationService = new();
    private readonly Mock<IHangfireJobQueueService> _mockJobQueueService = new();
    private readonly Mock<IOptions<ProvisionerConfiguration>> _mockProvisionerConfiguration = new();

    [Fact]
    public async Task HandleAsync_ShouldSendToProvisionService_WhenFeatureFlagIsEnabled()
    {
        // Arrange
        var config = new ProvisionerConfiguration { EnableProvisionService = true };
        _mockProvisionerConfiguration.Setup(x => x.Value).Returns(config);
        
        var order = CreateTestOrder();
        var redirectUrl = "https://test.com/redirect";
        var result = Result<(Order, string)>.Success((order, redirectUrl));
        
        _mockOrderRegistrationService
            .Setup(x => x.RegisterOrderAsync(It.IsAny<OrderUiRegisterRequest>(), false, It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);
        
        _mockJobQueueService
            .Setup(x => x.Schedule<IOrderProvisioner>(It.IsAny<System.Linq.Expressions.Expression<System.Action<IOrderProvisioner>>>(), It.IsAny<TimeSpan>()))
            .Returns("job-123");
        
        _mockRequestIdResolver.Setup(x => x.GetRequestId()).Returns("test-request-id");
        _mockIpResolver.Setup(x => x.GetIp()).Returns("127.0.0.1");

        var endpoint = Factory.Create<OrderUiRegisterEndpoint>(
            _mockClickhouseTaskBuilder.Object,
            _mockRequestIdResolver.Object,
            _mockOrderUpserter.Object,
            _mockIpResolver.Object,
            _mockContext.Object,
            _mockLogger.Object,
            _mockOrderRegistrationService.Object,
            _mockJobQueueService.Object,
            _mockProvisionerConfiguration.Object);

        var request = CreateTestRequest();

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        _mockJobQueueService.Verify(
            x => x.Schedule<IOrderProvisioner>(It.IsAny<System.Linq.Expressions.Expression<System.Action<IOrderProvisioner>>>(), It.IsAny<TimeSpan>()),
            Times.Once);
        
        _mockOrderUpserter.Verify(
            x => x.CreateOrderAsync(It.IsAny<Order>(), It.IsAny<string>(), It.IsAny<string>(), true, It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task HandleAsync_ShouldNotSendToProvisionService_WhenFeatureFlagIsDisabled()
    {
        // Arrange
        var config = new ProvisionerConfiguration { EnableProvisionService = false };
        _mockProvisionerConfiguration.Setup(x => x.Value).Returns(config);
        
        var order = CreateTestOrder();
        var redirectUrl = "https://test.com/redirect";
        var result = Result<(Order, string)>.Success((order, redirectUrl));
        
        _mockOrderRegistrationService
            .Setup(x => x.RegisterOrderAsync(It.IsAny<OrderUiRegisterRequest>(), false, It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(result);
        
        _mockRequestIdResolver.Setup(x => x.GetRequestId()).Returns("test-request-id");
        _mockIpResolver.Setup(x => x.GetIp()).Returns("127.0.0.1");

        var endpoint = Factory.Create<OrderUiRegisterEndpoint>(
            _mockClickhouseTaskBuilder.Object,
            _mockRequestIdResolver.Object,
            _mockOrderUpserter.Object,
            _mockIpResolver.Object,
            _mockContext.Object,
            _mockLogger.Object,
            _mockOrderRegistrationService.Object,
            _mockJobQueueService.Object,
            _mockProvisionerConfiguration.Object);

        var request = CreateTestRequest();

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        _mockJobQueueService.Verify(
            x => x.Schedule<IOrderProvisioner>(It.IsAny<System.Linq.Expressions.Expression<System.Action<IOrderProvisioner>>>(), It.IsAny<TimeSpan>()),
            Times.Never);
        
        _mockOrderUpserter.Verify(
            x => x.CreateOrderAsync(It.IsAny<Order>(), It.IsAny<string>(), It.IsAny<string>(), true, It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    private static Order CreateTestOrder()
    {
        return new Order
        {
            Id = OrderId.From(Guid.NewGuid()),
            ExternalId = ExternalId.From("test-external-id"),
            MerchantId = MerchantId.From("test-merchant"),
            CustomerId = CustomerId.From("test-customer"),
            Amount = 1000,
            Currency = CurrencyIso4217.USD,
            CreatedAt = DateTimeOffset.UtcNow,
            ExpiresInSeconds = 600,
            SecureSuccessToken = Ulid.NewUlid(),
            SecureFailureToken = Ulid.NewUlid()
        };
    }

    private static OrderUiRegisterRequest CreateTestRequest()
    {
        return new OrderUiRegisterRequest(
            ExternalId: "test-external-id",
            Amount: 1000,
            Currency: CurrencyIso4217.USD,
            CustomerId: "test-customer",
            Phone: "1234567890",
            Email: "<EMAIL>",
            ExpiresInSeconds: 600,
            StatusRedirectUrl: "https://test.com/status"
        );
    }
}
