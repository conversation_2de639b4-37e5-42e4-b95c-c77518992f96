using System.Linq.Expressions;
using FastEndpoints;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderConfirm;
using PaymentGateway.WebApi.Features.Orders.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace WebApi.UnitTests.Orders.OrderConfirm;

public class OrderConfirmEndpointTests
{
    private readonly Mock<IClaimsExtractor> _mockClaimExtractor = new();
    private readonly Mock<IClickhouseTaskBuilder> _mockClickhouseTaskBuilder = new();
    private readonly Mock<IIpResolver> _mockIpResolver = new();
    private readonly Mock<IHangfireJobQueueService> _mockJobQueueService = new();
    private readonly Mock<ILogger<OrderConfirmEndpoint>> _mockLogger = new();
    private readonly Mock<IOrderEventRepository> _mockOrderEventRepo = new();
    private readonly Mock<IOrderRepository> _mockOrderRepo = new();
    private readonly Mock<IRequestIdResolver> _mockRequestIdResolver = new();

    private OrderConfirmEndpoint SetupEndpoint()
    {
        _mockRequestIdResolver.Setup(r => r.GetRequestId())
            .Returns("test-request-id");

        _mockIpResolver.Setup(r => r.GetIp())
            .Returns("127.0.0.1");

        var endpoint = Factory.Create<OrderConfirmEndpoint>(_mockClickhouseTaskBuilder.Object, _mockOrderRepo.Object, _mockOrderEventRepo.Object, _mockRequestIdResolver.Object, _mockLogger.Object,
            _mockClaimExtractor.Object, _mockJobQueueService.Object, _mockIpResolver.Object);

        endpoint.HttpContext.Response.Body = new MemoryStream();

        return endpoint;
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnBadRequest_WhenStatusTokenIsNotValidUlid()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var request = new OrderConfirmRequest("invalid-token");

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status400BadRequest);

        _mockOrderRepo.Verify(r => r.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockOrderEventRepo.Verify(r => r.OrderIsClosedAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnBadRequest_WhenMerchantIdMissingInClaims()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validUlid = Ulid.NewUlid()
            .ToString();
        var request = new OrderConfirmRequest(validUlid);

        _mockClaimExtractor.Setup(m => m.GetMerchantIdAsString())
            .Returns((string?)null);

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status400BadRequest);

        // Nothing else should be called
        _mockOrderRepo.Verify(r => r.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockOrderEventRepo.Verify(r => r.OrderIsClosedAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnNotFound_WhenOrderNotFound()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validUlid = Ulid.NewUlid().ToString();
        var request = new OrderConfirmRequest(validUlid);

        _mockClaimExtractor.Setup(m => m.GetMerchantIdAsString())
            .Returns("MRC123");

        _mockOrderRepo.Setup(r => r.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Order?)null);

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status404NotFound);

        _mockOrderEventRepo.Verify(r => r.OrderIsClosedAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnBadRequest_WhenOrderIsAlreadyClosed()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validUlid = Ulid.NewUlid()
            .ToString();
        var request = new OrderConfirmRequest(validUlid);

        _mockClaimExtractor.Setup(m => m.GetMerchantIdAsString())
            .Returns("MRC123");

        var existingOrder = new Order
        {
            Id = OrderId.From(Guid.NewGuid()),
            MerchantId = MerchantId.From("MRC123"),
            SecureSuccessToken = Ulid.NewUlid(),
            SecureFailureToken = Ulid.NewUlid(),
            JobId = 123,
        };

        _mockOrderRepo.Setup(r => r.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingOrder);

        _mockOrderEventRepo.Setup(r => r.OrderIsClosedAsync(existingOrder.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status409Conflict);

        _mockOrderEventRepo.Verify(r => r.AddAsync(It.IsAny<OrderEvent>(), true, It.IsAny<CancellationToken>()), Times.Never);
        _mockJobQueueService.Verify(q => q.Delete(It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_ShouldMarkOrderSuccessAndDeleteHangfireJob_WhenSuccessTokenIsUsed()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var successTokenUlid = Ulid.NewUlid();
        var request = new OrderConfirmRequest(successTokenUlid.ToString());

        _mockClaimExtractor.Setup(m => m.GetMerchantIdAsString())
            .Returns("MRC123");

        var existingOrder = new Order
        {
            Id = OrderId.From(Guid.NewGuid()),
            MerchantId = MerchantId.From("MRC123"),
            SecureSuccessToken = successTokenUlid,
            SecureFailureToken = Ulid.NewUlid(),
            JobId = 55,
        };

        _mockOrderRepo.Setup(r => r.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingOrder);

        _mockOrderEventRepo.Setup(r => r.OrderIsClosedAsync(existingOrder.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _mockJobQueueService.Setup(q => q.Delete(existingOrder.JobId.ToString()))
            .Returns(true);

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status200OK);

        _mockOrderEventRepo.Verify(r => r.AddAsync(
            It.Is<OrderEvent>(evt => evt.OrderId == existingOrder.Id && evt.EventType == EventTypes.OrderPaymentSuccessful && evt.OrderStatus == OrderStatuses.Successful), true, // save == true
            It.IsAny<CancellationToken>()), Times.Once);

        _mockJobQueueService.Verify(q => q.Delete("55"), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_ShouldMarkOrderFailAndDeleteHangfireJob_WhenFailTokenIsUsed()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var failTokenUlid = Ulid.NewUlid();
        var request = new OrderConfirmRequest(failTokenUlid.ToString());

        _mockClaimExtractor.Setup(m => m.GetMerchantIdAsString())
            .Returns("MRC123");

        var existingOrder = new Order
        {
            Id = OrderId.From(Guid.NewGuid()),
            MerchantId = MerchantId.From("MRC123"),
            SecureSuccessToken = Ulid.NewUlid(),
            SecureFailureToken = failTokenUlid,
            JobId = 77,
        };

        _mockOrderRepo.Setup(r => r.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingOrder);

        _mockOrderEventRepo.Setup(r => r.OrderIsClosedAsync(existingOrder.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        _mockJobQueueService.Setup(q => q.Delete(existingOrder.JobId.ToString()))
            .Returns(true);

        // Act
        await endpoint.HandleAsync(request, CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status200OK);

        _mockOrderEventRepo.Verify(
            r => r.AddAsync(It.Is<OrderEvent>(evt => evt.OrderId == existingOrder.Id && evt.EventType == EventTypes.OrderPaymentFailed && evt.OrderStatus == OrderStatuses.FailedNoRetry), true,
                It.IsAny<CancellationToken>()), Times.Once);

        _mockJobQueueService.Verify(q => q.Delete("77"), Times.Once);
    }
}
