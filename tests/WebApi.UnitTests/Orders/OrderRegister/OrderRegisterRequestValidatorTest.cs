using FluentValidation.TestHelper;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Orders.OrderRegister;

namespace WebApi.UnitTests.Orders.OrderRegister;

public class OrderRegisterRequestValidatorTest
{
    private readonly OrderRegisterRequestValidator _validator = new();

    [Theory]
    [InlineData(0)]
    [InlineData(-1)]
    public void Validator_ShouldHaveError_WhenAmountIsLessThanOrEqualZero(int amount)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, amount, CurrencyIso4217.USD, null, null, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Amount)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderAmountMustBeGreaterThanZero.ToString());
    }

    [Theory]
    [InlineData(12)]
    [InlineData(1)]
    public void Validator_ShouldNoHaveError_WhenAmountIsGreaterThanZero(int amount)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, amount, CurrencyIso4217.USD, null, null, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Amount);
    }

    [Theory]
    [InlineData(CurrencyIso4217.IQD)]
    [InlineData(CurrencyIso4217.USD)]

    public void Validator_ShouldNotHaveError_WhenCurrencyIsValid(CurrencyIso4217 currency)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, currency, null, null, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Currency);
    }

    [Fact]
    public void Validator_ShouldHaveError_WhenCurrencyIsInvalid()
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, (CurrencyIso4217)4, null, null, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Currency)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderCurrencyMustBeValid.ToString());
    }

    [Theory]
    [InlineData("07512345678")]
    [InlineData("07712345678")]
    [InlineData("07812345678")]
    [InlineData("07912345678")]
    [InlineData("")]
    public void Validator_ShouldNotHaveError_WhenValidPhoneNumberOrEmpty(string phone)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, CurrencyIso4217.USD, null, phone, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Phone);
    }

    [Theory]
    [InlineData("0751234567")]
    [InlineData("0771234567")]
    [InlineData("0781234567")]
    [InlineData("0791234567")]
    [InlineData("07312345678")]
    public void Should_Validate_Phone_Based_On_Regex(string phone)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, CurrencyIso4217.USD, null, phone, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Phone)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderPhoneMustBeStartWith07.ToString());
    }
        
    [Theory]
    [InlineData("x @x.com")]
    [InlineData("<EMAIL>")]
    [InlineData(".@x.com")]
    [InlineData("x@x")]
    [InlineData("x@x.")]
    [InlineData("x@x.x")]
    [InlineData("x@x.x.")]
    [InlineData("x@x.x.x")]
    [InlineData("x@x.x.x.")]
    [InlineData("x@x_")]
    [InlineData("x@_.com")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("ق@_.com")]
    [InlineData("ي@s.com")]
    [InlineData(" ")]
    [InlineData("")]
    [InlineData("<EMAIL>")]

        
    public void Email_ShouldNotBeValid_WhenEmailIsInvalid(string email)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, CurrencyIso4217.USD, null, null, email, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Email)
            .WithErrorCode(ErrorNumbers.OrderErrors.OrderEmailMustBeValid.ToString());
    }
        
    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData(null)]
    [InlineData("<EMAIL>")]

    public void Email_ShouldBeValid_WhenEmailIsValidOrNull(string? email)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, CurrencyIso4217.USD, null, null, email, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Email);
    }
    
    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(";;'ksd[gpka s[dpkg[ [podkfpgojdpfogj pojdfpgo jdfpgo jdpfogj pdfogj dffdfgdfgf sdfhg poiushdpgoihsdfpogihsdfpoighposdifh s dsfpogihsdfoigh oishdfogihsdfpoigh oisdfhgoihfsdgoihsdpofighposdifhgpoisdfhgpoihsdfpogiksajdg;jas;dgja;jojasd;asdogjpoasjdgpoasjdgpojsadpojgasdg")]
    public void CustomerId_ShouldNotBeValid_WhenNotMeetValidationRules(string customerId)
    {
        // Arrange
        var request = new OrderRegisterRequest(null, 10, CurrencyIso4217.IQD, customerId, null, null, 600);

        // Act
        var result = _validator.TestValidate(request);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.CustomerId);
    }
}