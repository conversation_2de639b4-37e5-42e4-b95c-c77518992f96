// using FastEndpoints;
// using FluentAssertions;
// using Moq;
// using PaymentGateway.Integrations.Services.Tabadul;
// using PaymentGateway.WebApi.Common;
// using PaymentGateway.WebApi.Common.Services;
// using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Orders.OrderRegister;
// using PaymentGateway.WebApi.Features.Orders.Services;
// using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;
//
// namespace WebApi.UnitTests.Orders.OrderRegister;
//
// public class OrderRegisterEndpointTest
// {
//     private readonly Mock<IClickhouseTaskBuilder> _mockClickhouseTaskBuilder = new();
//     private readonly Mock<IRequestIdResolver> _requestIdResolver = new();
//     private readonly Mock<IOrderRegistrationService> _mockOrderRegistrationService = new();
//     private readonly Mock<IOrderUpserter> _mockOrderUpserter = new();
//     private readonly Mock<IIpResolver> _mockIpResolver = new();
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturn201Created_OnSuccess()
//     {
//         // Arrange
//         var endpoint = SetupEndpoint();
//
//         var request = new OrderRegisterRequest(ExternalId: null, Amount: 500, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: "123456789", Email: "<EMAIL>", ExpiresInSeconds: 0);
//
//         var expectedOrder = new Order()
//         {
//             SecureSuccessToken = default, SecureFailureToken = default, Id = OrderId.From(Guid.NewGuid())
//         };
//         var expectedReturnUrl = "https://example.com";
//
//         _mockOrderRegistrationService.Setup(s => s.RegisterOrderAsync(It.IsAny<OrderRegisterBaseRequest>(), It.IsAny<bool>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(Result<(Order order, string url)>.Success((expectedOrder, expectedReturnUrl)));
//
//         _mockClickhouseTaskBuilder
//             .Setup(x => x.SendToClickhouse(It.IsAny<OrderRegisterRequest>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Order>(), It.IsAny<CancellationToken>()))
//             .Verifiable();        
//     
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//         endpoint.HttpContext
//             .Response
//             .StatusCode
//             .Should()
//             .Be(201);
//         endpoint.ResponseStarted
//             .Should()
//             .BeTrue();
//
//         _mockClickhouseTaskBuilder.Verify(x => x.SendToClickhouse(It.IsAny<OrderRegisterRequest>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Order>(), It.IsAny<CancellationToken>()),
//             Times.Once);
//     }
//
//     private OrderRegisterEndpoint SetupEndpoint()
//     {
//         _mockClickhouseTaskBuilder
//             .Setup(x => x.SendToClickhouse(It.IsAny<OrderRegisterRequest>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Order>(), It.IsAny<CancellationToken>()))
//             .Verifiable();        
//         var endpoint = Factory.Create<OrderRegisterEndpoint>(
//             _mockClickhouseTaskBuilder.Object,
//             _mockOrderUpserter.Object,
//             _mockOrderRegistrationService.Object,
//             _mockIpResolver.Object,
//             _requestIdResolver.Object
//             );
//         return endpoint;
//     }
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturnProblem_OnFailure()
//     {
//         // Arrange
//         var endpoint = SetupEndpoint();
//
//         var request = new OrderRegisterRequest(ExternalId: null, Amount: 500, Currency: CurrencyIso4217.USD, CustomerId: null, Phone: "123456789", Email: "<EMAIL>", ExpiresInSeconds: 0);
//         
//         _mockOrderRegistrationService.Setup(s => s.RegisterOrderAsync(It.IsAny<OrderRegisterBaseRequest>(), It.IsAny<bool>(), It.IsAny<string?>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(Result<(Order order, string url)>.Failure(new Error(ErrorNumbers.TabadulError, "Tabadul error code", "Tabadul Error message")));
//
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//         endpoint.ResponseStarted
//             .Should()
//             .BeTrue();
//         endpoint.HttpContext
//             .Response
//             .StatusCode
//             .Should()
//             .NotBe(201);
//     }
// }
