using System.Linq.Expressions;
using FastEndpoints;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.Integrations.Services.Tabadul.OrderGetStatusModels;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderCancel;
using PaymentGateway.WebApi.Features.Orders.OrderConfirm;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Payments.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace WebApi.UnitTests.Orders.OrderCancel;

public class OrderCancelEndpointTests
{
    private readonly Mock<IClaimsExtractor> _mockClaimsExtractor = new();
    private readonly Mock<IClickhouseTaskBuilder> _mockClickhouseTaskBuilder = new();
    private readonly Mock<IIpResolver> _mockIpResolver = new();
    private readonly Mock<IHangfireJobQueueService> _mockJobQueueService = new();
    private readonly Mock<ILogger<OrderConfirmEndpoint>> _mockLogger = new();
    private readonly Mock<IOrderEventRepository> _mockOrderEventRepository = new();
    private readonly Mock<IOrderRepository> _mockOrderRepository = new();
    private readonly Mock<IRequestIdResolver> _mockRequestIdResolver = new();
    private readonly Mock<ITabadulOrderService> _mockTabadulOrderService = new();

    private OrderCancelEndpoint SetupEndpoint()
    {
        _mockRequestIdResolver.Setup(r => r.GetRequestId()).Returns("test-request-id");
        _mockIpResolver.Setup(r => r.GetIp()).Returns("127.0.0.1");

        var endpoint = Factory.Create<OrderCancelEndpoint>(
            _mockClickhouseTaskBuilder.Object,
            _mockOrderRepository.Object,
            _mockOrderEventRepository.Object,
            _mockRequestIdResolver.Object,
            _mockLogger.Object,
            _mockClaimsExtractor.Object,
            _mockJobQueueService.Object,
            _mockTabadulOrderService.Object,
            _mockIpResolver.Object);

        endpoint.HttpContext.Response.Body = new MemoryStream();
        return endpoint;
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnBadRequest_WhenBothOrderIdAndExternalIdAreInvalid()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var request = new OrderCancelRequest("invalid-order-id", null);

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("The provided order iD is not valid")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnBadRequest_WhenMerchantIdIsMissing()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var request = new OrderCancelRequest(validOrderId.ToString(), null);
        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns((string?)null);

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("MerchantId is missing in the claims")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnNotFound_WhenOrderByExternalIdDoesNotExist()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var externalId = Ulid.NewUlid().ToString();
        var request = new OrderCancelRequest(null, externalId);
        _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Order?)null);

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.GetType().Name.Should().Contain("NotFound");
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnNotFound_WhenOrderByOrderIdDoesNotExist()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var request = new OrderCancelRequest(validOrderId.ToString(), null);
        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns("MRC123");
        _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Order?)null);

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.GetType().Name.Should().Contain("NotFound");
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnBadRequest_WhenOrderCannotBeCanceled()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var request = new OrderCancelRequest(validOrderId.ToString(), null);
        var order = CreateTestOrder(validOrderId);

        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns("MRC123");
        _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(order);
        _mockOrderEventRepository.Setup(x => x.OrderCanBeCanceledAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnBadRequest_WhenTabadulValidationFails()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var request = new OrderCancelRequest(validOrderId.ToString(), null);
        var order = CreateTestOrder(validOrderId);
        var tabadulError = new Error(ErrorNumbers.TabadulError, "TabadulError", "Tabadul service error");

        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns("MRC123");
        _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(order);
        _mockOrderEventRepository.Setup(x => x.OrderCanBeCanceledAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockTabadulOrderService.Setup(x => x.FindOrderStatus(It.IsAny<Guid>(), It.IsAny<MerchantId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<OrderGetStatusResponse>.Failure(tabadulError));

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to fetch order status from Tabadul")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnBadRequest_WhenOrderStatusIsNotOrderRegistered()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var request = new OrderCancelRequest(validOrderId.ToString(), null);
        var order = CreateTestOrder(validOrderId);
        var tabadulResponse = CreateTabadulResponse(validOrderId, TabadulOrderStatuses.DepositedSuccessfully);

        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns("MRC123");
        _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(order);
        _mockOrderEventRepository.Setup(x => x.OrderCanBeCanceledAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockTabadulOrderService.Setup(x => x.FindOrderStatus(It.IsAny<Guid>(), It.IsAny<MerchantId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<OrderGetStatusResponse>.Success(tabadulResponse));

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnOk_WhenOrderCancellationIsSuccessfulWithOrderId()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var request = new OrderCancelRequest(validOrderId.ToString(), null);
        var order = CreateTestOrder(validOrderId);
        var tabadulResponse = CreateTabadulResponse(validOrderId, TabadulOrderStatuses.OrderRegistered);

        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns("MRC123");
        _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(order);
        _mockOrderEventRepository.Setup(x => x.OrderCanBeCanceledAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockTabadulOrderService.Setup(x => x.FindOrderStatus(It.IsAny<Guid>(), It.IsAny<MerchantId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<OrderGetStatusResponse>.Success(tabadulResponse));
        _mockJobQueueService.Setup(x => x.Delete(It.IsAny<string>())).Returns(true);
        _mockJobQueueService.Setup(x => x.Enqueue<IMerchantNotifier>(It.IsAny<Expression<Action<IMerchantNotifier>>>()))
            .Returns("job-123");

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.GetType().Name.Should().Contain("Ok");

        _mockOrderEventRepository.Verify(
            x => x.AddAsync(
                It.Is<OrderEvent>(oe =>
                    oe.OrderId == order.Id &&
                    oe.EventType == EventTypes.OrderCanceled &&
                    oe.OrderStatus == OrderStatuses.Canceled &&
                    oe.Description == "Order canceled by customer" &&
                    oe.EventDetails == "null" &&
                    oe.IpAddress == "127.0.0.1" &&
                    oe.RequestId == "test-request-id"),
                true,
                It.IsAny<CancellationToken>()),
            Times.Once);

        _mockJobQueueService.Verify(x => x.Delete(order.JobId.ToString()), Times.Once);
        _mockJobQueueService.Verify(x => x.Enqueue<IMerchantNotifier>(It.IsAny<Expression<Action<IMerchantNotifier>>>()), Times.Once);
        _mockClickhouseTaskBuilder.Verify(
            x => x.SendToClickhouse(
                It.IsAny<OrderId>(),
                "127.0.0.1",
                "test-request-id",
                order,
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task ExecuteAsync_ShouldReturnOk_WhenOrderCancellationIsSuccessfulWithExternalId()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validOrderId = Guid.NewGuid();
        var externalId = Ulid.NewUlid().ToString();
        var request = new OrderCancelRequest(null, externalId);
        var order = CreateTestOrder(validOrderId);
        var tabadulResponse = CreateTabadulResponse(validOrderId, TabadulOrderStatuses.OrderRegistered);

        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString()).Returns("MRC123");
        _mockOrderRepository.SetupSequence(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(order)
            .ReturnsAsync(order);
        _mockOrderEventRepository.Setup(x => x.OrderCanBeCanceledAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
        _mockTabadulOrderService.Setup(x => x.FindOrderStatus(It.IsAny<Guid>(), It.IsAny<MerchantId>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(Result<OrderGetStatusResponse>.Success(tabadulResponse));
        _mockJobQueueService.Setup(x => x.Delete(It.IsAny<string>())).Returns(true);
        _mockJobQueueService.Setup(x => x.Enqueue<IMerchantNotifier>(It.IsAny<Expression<Action<IMerchantNotifier>>>()))
            .Returns("job-123");

        // Act
        var result = await endpoint.ExecuteAsync(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.GetType().Name.Should().Contain("Ok");
    }

    private static Order CreateTestOrder(Guid orderId)
    {
        return new Order
        {
            Id = OrderId.From(orderId),
            MerchantId = MerchantId.From("MRC123"),
            ExternalId = ExternalId.From(Ulid.NewUlid().ToString()),
            Amount = 1000,
            Currency = CurrencyIso4217.USD,
            CustomerId = CustomerId.From("CUST123"),
            ExpiresInSeconds = 3600,
            Phone = "0791234567",
            Email = "<EMAIL>",
            CreatedAt = DateTimeOffset.UtcNow,
            SecureSuccessToken = Ulid.NewUlid(),
            SecureFailureToken = Ulid.NewUlid(),
            JobId = 12345
        };
    }

    private static OrderGetStatusResponse CreateTabadulResponse(Guid orderId, TabadulOrderStatuses status)
    {
        return new OrderGetStatusResponse
        {
            OrderNumber = orderId.ToString(),
            OrderStatus = status,
            ErrorCode = TabadulErrorCode.Success,
            ErrorMessage = null,
            Amount = 1000,
            DepositAmount = 1000,
            Currency = CurrencyIso4217.USD
        };
    }
}