using System.Linq.Expressions;
using FastEndpoints;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.Integrations.Services.Tabadul.OrderGetStatusModels;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderCancel;
using PaymentGateway.WebApi.Features.Orders.OrderConfirm;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Payments.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace WebApi.UnitTests.Orders.OrderCancel;

public class OrderCancelEndpointTests
{
    
}
