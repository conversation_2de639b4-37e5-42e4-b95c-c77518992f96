using System.Reflection;
using FastEndpoints;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderFind;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace WebApi.UnitTests.Orders.OrderFind;

public class OrdersDetailsEndpointTests
{
    private readonly Mock<IOrderRepository> _mockRepository = new();

    private OrdersDetailsEndpoint SetupEndpoint()
    {
        var endpoint = Factory.Create<OrdersDetailsEndpoint>(_mockRepository.Object);

        var mockHttpContext = new DefaultHttpContext();
        mockHttpContext.Request.RouteValues["id"] = Guid.NewGuid()
            .ToString();

        typeof(Endpoint).GetProperty("HttpContext", BindingFlags.Instance | BindingFlags.NonPublic)
            ?.SetValue(endpoint, mockHttpContext);

        return endpoint;
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnBadRequest_WhenOrderIDIsInvalid()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        endpoint.HttpContext.Request.RouteValues["id"] = "InvalidGuid";

        // Act
        await endpoint.HandleAsync(CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status400BadRequest);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnNotFound_WhenOrderDoesNotExist()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validGuid = Guid.NewGuid();
        endpoint.HttpContext.Request.RouteValues["id"] = validGuid.ToString();

        _mockRepository.Setup(repo => repo.FindAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((OrdersDetailsResponse?)null);

        // Act
        await endpoint.HandleAsync(CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status404NotFound);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnOrderDetails_WhenOrderExists()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validGuid = Guid.NewGuid();
        var mockOrder = new Order
        {
            Id = OrderId.From(validGuid),
            ExternalId = ExternalId.From(Guid.NewGuid()
                .ToString()),
            Amount = 1000,
            Currency = CurrencyIso4217.USD,
            CustomerId = CustomerId.From("Cust123"),
            Phone = "123456789",
            Email = "<EMAIL>",
            SecureSuccessToken = Ulid.NewUlid(),
            SecureFailureToken = Ulid.NewUlid(),
        };

        var mockResponse = new OrdersDetailsResponse
        {
            Id = validGuid,
            ExternalId = mockOrder.ExternalId.Value,
            Amount = mockOrder.Amount,
            Currency = mockOrder.Currency,
            CustomerId = mockOrder.CustomerId.Value,
            Phone = mockOrder.Phone,
            Email = mockOrder.Email,
            CreatedAt = DateTimeOffset.UtcNow,
            MerchantId = "",
        };

        endpoint.HttpContext.Request.RouteValues["id"] = validGuid.ToString();

        _mockRepository.Setup(repo => repo.FindAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockResponse);


        // Act
        await endpoint.HandleAsync(CancellationToken.None);

        // Assert
        var response = endpoint.Response;
        response.Should()
            .NotBeNull();
        response.Id
            .Should()
            .Be(mockResponse.Id);
        response.Amount
            .Should()
            .Be(mockResponse.Amount);
        response.Currency
            .Should()
            .Be(mockResponse.Currency);
        endpoint.HttpContext
            .Response
            .StatusCode
            .Should()
            .Be(StatusCodes.Status200OK);
    }

    [Fact]
    public async Task HandleAsync_ShouldReturnCorrectContentType()
    {
        // Arrange
        var endpoint = SetupEndpoint();
        var validGuid = Guid.NewGuid();
        endpoint.HttpContext.Request.RouteValues["id"] = validGuid.ToString();

        _mockRepository.Setup(repo => repo.FindAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new OrdersDetailsResponse
            {
                Id = Guid.Empty,
                Currency = 0,
                CreatedAt = default,
                MerchantId = "M",
            });

        // Act
        await endpoint.HandleAsync(CancellationToken.None);

        // Assert
        endpoint.HttpContext
            .Response
            .ContentType
            .Should()
            .Contain("application/json");
    }
}
