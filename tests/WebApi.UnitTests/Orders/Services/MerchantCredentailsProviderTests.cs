using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul.Auth;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Services;

namespace WebApi.UnitTests.Orders.Services;

public class MerchantCredentialProviderTests
{
    private readonly PaymentDbContext _dbContext;
    private readonly Mock<ICachingService> _mockCachingService;
    private readonly Mock<IClaimsExtractor> _mockClaimsExtractor;

    public MerchantCredentialProviderTests()
    {
        _mockClaimsExtractor = new Mock<IClaimsExtractor>();
        _mockCachingService = new Mock<ICachingService>();

        var dbOptions = new DbContextOptionsBuilder<PaymentDbContext>().UseInMemoryDatabase(Guid.NewGuid()
                .ToString())
            .Options;

        _dbContext = new PaymentDbContext(dbOptions);
        _dbContext.Database.EnsureCreated();
    }

    [Fact]
    public async Task GetMerchantCredentialsAsync_ThrowsException_WhenMerchantIdClaimIsNull()
    {
        // Arrange
        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString())
            .Returns((string?)null);

        var provider = new MerchantCredentialProvider(_dbContext, _mockClaimsExtractor.Object, _mockCachingService.Object);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => provider.GetMerchantCredentialsAsync("AnyMerchantId", CancellationToken.None));
    }

    [Fact]
    public async Task GetMerchantCredentialsAsync_ThrowsException_WhenMerchantIdIsInvalid()
    {
        // Arrange
        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString())
            .Returns("Invalid-Merchant-Id"); // or any format that fails MerchantId.TryFrom

        var provider = new MerchantCredentialProvider(_dbContext, _mockClaimsExtractor.Object, _mockCachingService.Object);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => provider.GetMerchantCredentialsAsync("AnyMerchantId", CancellationToken.None));
    }

    [Fact]
    public async Task GetMerchantCredentialsAsync_ReturnsCredentialsFromCache_WhenAvailable()
    {
        // Arrange
        var validMerchantIdString = "12345";
        
        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString())
            .Returns(validMerchantIdString);

        var cachedCredentials = new MerchantCredentialModel("cachedUser", "cachedPass");

        _mockCachingService.Setup(x => x.GetAsync<MerchantCredentialModel>($"{AppConstants.CachePrefixes.MerchantCredentialPrefix}12345"))
            .ReturnsAsync(cachedCredentials);

        var provider = new MerchantCredentialProvider(_dbContext, _mockClaimsExtractor.Object, _mockCachingService.Object);

        // Act
        var result = await provider.GetMerchantCredentialsAsync(validMerchantIdString, CancellationToken.None);

        // Assert
        Assert.Equal(cachedCredentials.Username, result.Username);
        Assert.Equal(cachedCredentials.Password, result.Password);

        _mockCachingService.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<MerchantCredentialModel>()), Times.Never);
    }

    [Fact]
    public async Task GetMerchantCredentialsAsync_ReturnsCredentialsFromDbAndCachesThem_WhenNotInCache()
    {
        // Arrange
        var validMerchantIdString = "1000";

        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString())
            .Returns(validMerchantIdString);

        _mockCachingService.Setup(x => x.GetAsync<MerchantCredentialModel>(It.IsAny<string>()))
            .ReturnsAsync((MerchantCredentialModel?)null);

        var merchantId = MerchantId.From(validMerchantIdString);
        var merchantLoginId = MerchantLoginId.From(Ulid.NewUlid());
        var merchantTabadulLoginId = MerchantTabadulLoginId.From(Ulid.NewUlid());
        var merchantLogin = new MerchantLogin
        {
            MerchantId = merchantId,
            MerchantTabadulLogin = new MerchantTabadulLogin
            {
                TabadulUsername = "dbUsername", TabadulPassword = "dbPassword", Id = merchantTabadulLoginId,
            },
            ApiKey = "api_key",
            Id = merchantLoginId,
            MerchantTabadulLoginId = merchantTabadulLoginId
        };

        _dbContext.MerchantLogins.Add(merchantLogin);
        await _dbContext.SaveChangesAsync();

        var provider = new MerchantCredentialProvider(_dbContext, _mockClaimsExtractor.Object, _mockCachingService.Object);

        // Act
        var result = await provider.GetMerchantCredentialsAsync(validMerchantIdString, CancellationToken.None);

        // Assert
        result.Username.Should().Be("dbUsername");
        result.Password.Should().Be("dbPassword");

        _mockCachingService.Verify(
            x => x.SetAsync<MerchantCredentialModel>($"{AppConstants.CachePrefixes.MerchantCredentialPrefix}{merchantId}",
                It.Is<MerchantCredentialModel>(m => m.Username == "dbUsername" && m.Password == "dbPassword")), Times.Once);
    }

    [Fact]
    public async Task GetMerchantCredentialsAsync_ThrowsException_WhenCredentialsNotInDatabase()
    {
        // Arrange
        var validMerchantIdString = "2000";
        _mockClaimsExtractor.Setup(x => x.GetMerchantIdAsString())
            .Returns(validMerchantIdString);

        // Cache returns null
        _mockCachingService.Setup(x => x.GetAsync<MerchantCredentialModel>(It.IsAny<string>()))
            .ReturnsAsync((MerchantCredentialModel?)null);
        

        var provider = new MerchantCredentialProvider(_dbContext, _mockClaimsExtractor.Object, _mockCachingService.Object);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => provider.GetMerchantCredentialsAsync(validMerchantIdString, CancellationToken.None));
    }
}
