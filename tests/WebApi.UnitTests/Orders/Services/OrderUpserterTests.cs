using FluentAssertions;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Orders.Shared;
using OrderStatuses = PaymentGateway.WebApi.Features.Orders.Data.OrderStatuses;

namespace WebApi.UnitTests.Orders.Services
{
    public class OrderUpserterTests
    {
        private readonly Mock<IOrderRepository> _orderRepositoryMock = new();
        private readonly Mock<IOrderEventRepository> _orderEventRepositoryMock = new ();
        private readonly Mock<IIpResolver> _ipResolverMock = new();
        private readonly Mock<IRequestIdResolver> _requestIdResolverMock = new();

        [Fact]
        public async Task CreateOrder_Success_ReturnsSuccessResult()
        {
            // Arrange
            var order = new Order
            {
                Id = OrderId.From(Guid.NewGuid()),
                MerchantId = MerchantId.From("MRC001"),
                ExternalId = ExternalId.From(Guid.NewGuid().ToString()),
                Currency = CurrencyIso4217.IQD,
                ExpiresInSeconds = 300,
                CreatedAt = DateTimeOffset.UtcNow,
                Amount = 500,
                Email = "",
                Phone = "EEE",
                CustomerId = CustomerId.From("CST001"),
                SecureSuccessToken = Ulid.NewUlid(),
                SecureFailureToken = Ulid.NewUlid()
            };

            _orderRepositoryMock
                .Setup(repo => repo.AddAsync(order,It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(order);

            _orderEventRepositoryMock
                .Setup(repo => repo.AddAsync(It.IsAny<OrderEvent>(),It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new OrderEvent
                {
                    OrderId = order.Id,
                    EventDetails = string.Empty,
                    EventType = EventTypes.OrderCreated,
                    OrderStatus = OrderStatuses.Initiated,
                    CreatedAt = default,
                    IpAddress = null,
                    RequestId = null,
                    Description = null,
                    Id = OrderEventId.From(Ulid.NewUlid())
                });

            var expectedIpAddress = "*************";
            var expectedRequestId = "123456";
            _ipResolverMock.Setup(ip => ip.GetIp()).Returns(expectedIpAddress);
            _requestIdResolverMock.Setup(req => req.GetRequestId()).Returns(expectedRequestId);
            
           
            // Act
            await new OrderUpserter(_orderEventRepositoryMock.Object, _orderRepositoryMock.Object)
                .CreateOrderAsync(order,expectedRequestId,expectedIpAddress,true, "Test Description", CancellationToken.None);

            // Assert

            _orderRepositoryMock.Verify(repo => repo.AddAsync(order, It.IsAny<bool>(),It.IsAny<CancellationToken>()), Times.Once);
            
            _orderEventRepositoryMock.Verify(repo => repo.AddAsync(It.Is<OrderEvent>(oe =>
                oe.OrderId == order.Id &&
                oe.EventType == EventTypes.OrderCreated &&
                oe.Description == "Test Description" &&
                oe.OrderStatus == OrderStatuses.Initiated &&
                !string.IsNullOrEmpty(oe.EventDetails)
            ), It.IsAny<bool>(),It.IsAny<CancellationToken>()), Times.Once);
        }

    }
}
