using System.Linq.Expressions;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.Integrations.Services.Tabadul.OrderRegisterModels;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderRegister;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Orders.Shared;
using IMapper = AutoMapper.IMapper;

namespace WebApi.UnitTests.Orders.Services
{
    public class OrderRegistrationServiceTest
    {
        private readonly Mock<IClaimsExtractor> _mockClaimsExtractor = new();
        private readonly Mock<ITabadulOrderService> _mockExternalOrderService = new();
        private readonly Mock<ILogger<IOrderRegistrationService>> _mockLogger = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private readonly Mock<IMerchantConfigRepository> _mockMerchantConfigRepository = new();
        private readonly Mock<IOpenedOrderDuplicatedBlocker> _mockOpenedOrderDuplicatedBlocker = new();
        private readonly Mock<IRequestIdResolver> _mockRequestIdResolver = new();

        private readonly OrderConfiguration _orderConfiguration = new()
        {
            ExpiresInSeconds = 300,
        };

        private readonly Order _defaultOrder = new()
        {
            Id = OrderId.From(Guid.NewGuid()),
            MerchantId = MerchantId.From("MRC001"),
            ExternalId = ExternalId.From("Default-External-Id"),
            Amount = 10,
            Currency = CurrencyIso4217.USD,
            ExpiresInSeconds = 600,
            CustomerId = CustomerId.From(AppConstants.DefaultCustomerId),
            CreatedAt = DateTimeOffset.UtcNow,
            SecureSuccessToken = Ulid.NewUlid(),
            SecureFailureToken = Ulid.NewUlid()
        };
        private void InitializeBaseMocks()
        {
            _mockRequestIdResolver
                .Setup(r => r.GetRequestId())
                .Returns("request_id_123");
            
            _mockClaimsExtractor
                .Setup(c => c.GetMerchantIdAsString())
                .Returns("MERCHANT-123");

            _mockMapper
                .Setup(m => m.Map<Order>(It.IsAny<OrderRegisterBaseRequest>()))
                .Returns(_defaultOrder);
            
            _mockOpenedOrderDuplicatedBlocker
                .Setup(b => b.HasOpenedOrderAsync(It.IsAny<CustomerId>(), It.IsAny<MerchantId>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<bool>.Success(false));

            _mockExternalOrderService
                .Setup(s => s.RegisterOrderAsync(It.IsAny<Order>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(() =>
                {
                    var tabadulResponse = new TabadulOrderRegisterResponse
                    {
                        OrderId = Guid.NewGuid(),
                        FormUrl = "https://example.com/fake-form-url"
                    };
                    return Result<TabadulOrderRegisterResponse>.Success(tabadulResponse);
                });
        }

        private IOrderRegistrationService InitializeService_MerchantConfig_AndPreventDuplicates()
        {
            InitializeBaseMocks(); 

            var merchantConfig = new MerchantConfig
            {
                Id = MerchantConfigId.From(Ulid.NewUlid()),
                PublicAPIUrl = "https://example.com",
                MerchantId = MerchantId.From("MERCHANT-123"),
                PreventDuplicateCustomerOrders = true,
                MaxNotificationRetries = 3,
            };

            _mockMerchantConfigRepository
                .Setup(r => r.FindAsync(It.IsAny<Expression<Func<MerchantConfig, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(merchantConfig);

            return new OrderRegistrationService(
                _mockMapper.Object,
                _mockRequestIdResolver.Object,
                _mockLogger.Object,
                _mockExternalOrderService.Object,
                _mockClaimsExtractor.Object,
                _mockMerchantConfigRepository.Object,
                _mockOpenedOrderDuplicatedBlocker.Object,
                _orderConfiguration
            );
        }

        
        private IOrderRegistrationService InitializeService_MerchantConfig_NoPreventDuplicates()
        {
            InitializeBaseMocks();

            var merchantConfig = new MerchantConfig
            {
                Id = MerchantConfigId.From(Ulid.NewUlid()),
                PublicAPIUrl = "https://example.com",
                MerchantId = MerchantId.From("MERCHANT-123"),
                PreventDuplicateCustomerOrders = false,
                MaxNotificationRetries = 3,
            };

            _mockMerchantConfigRepository
                .Setup(r => r.FindAsync(It.IsAny<Expression<Func<MerchantConfig, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(merchantConfig);

            return new OrderRegistrationService(
                _mockMapper.Object,
                _mockRequestIdResolver.Object,
                _mockLogger.Object,
                _mockExternalOrderService.Object,
                _mockClaimsExtractor.Object,
                _mockMerchantConfigRepository.Object,
                _mockOpenedOrderDuplicatedBlocker.Object,
                _orderConfiguration
            );
        }

        private IOrderRegistrationService InitializeService_NoMerchantConfig()
        {
            InitializeBaseMocks();

            _mockMerchantConfigRepository
                .Setup(r => r.FindAsync(It.IsAny<Expression<Func<MerchantConfig, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((MerchantConfig?)null);

            return new OrderRegistrationService(
                _mockMapper.Object,
                _mockRequestIdResolver.Object,
                _mockLogger.Object,
                _mockExternalOrderService.Object,
                _mockClaimsExtractor.Object,
                _mockMerchantConfigRepository.Object,
                _mockOpenedOrderDuplicatedBlocker.Object,
                _orderConfiguration
            );
        }
        
        [Fact]
        public async Task RegisterOrderAsync_ShouldReturnFailure_IfMerchantIdIsMissing()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            _mockClaimsExtractor
                .Setup(c => c.GetMerchantIdAsString())
                .Returns((string?)null);

            // Act
            var request = CreateCommonRequest();
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Should().Be(OrderErrors.MerchantIdMissing);
            result.Data.Should().Be((null, null)!);
        }

        [Fact]
        public async Task RegisterOrderAsync_ShouldReturnFailure_IfMerchantConfigIsMissing()
        {
            var service = InitializeService_NoMerchantConfig();

            // Act
            var request = CreateCommonRequest();
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Should().Be(OrderErrors.MerchantConfigIsMissing);
            result.Data.Should().Be((null, null)!);
        }

        [Fact]
        public async Task RegisterOrderAsync_ShouldCallOpenedOrderDuplicatedBlocker_IfPreventDuplicateIsTrue()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            var request = CreateCommonRequest(customerId: null);
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            _mockOpenedOrderDuplicatedBlocker.Verify(
                b => b.HasOpenedOrderAsync(
                    It.Is<CustomerId>(cid => cid.Value == AppConstants.DefaultCustomerId),
                    It.Is<MerchantId>(mid => mid.Value == "MERCHANT-123"),
                    It.IsAny<CancellationToken>()),
                Times.Once
            );
        }

        [Fact]
        public async Task RegisterOrderAsync_ShouldNotCallOpenedOrderDuplicatedBlocker_IfPreventDuplicateIsFalse()
        {
            var service = InitializeService_MerchantConfig_NoPreventDuplicates();

            var request = CreateCommonRequest(customerId: "CUSTOMER-ABC");
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            _mockOpenedOrderDuplicatedBlocker.Verify(
                b => b.HasOpenedOrderAsync(It.IsAny<CustomerId>(), It.IsAny<MerchantId>(), It.IsAny<CancellationToken>()),
                Times.Never
            );
        }
        
        [Fact]
        public async Task RegisterOrderAsync_ShouldReturnFailure_IfOpenedOrderDetected()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            _mockOpenedOrderDuplicatedBlocker
                .Setup(b => b.HasOpenedOrderAsync(It.IsAny<CustomerId>(), It.IsAny<MerchantId>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<bool>.Success(true));

            var request = CreateCommonRequest();
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Should().Be(OrderErrors.OpenedOrderDetected);
            result.Data.Should().Be((null, null)!);
        }
        
        [Fact]
        public async Task RegisterOrderAsync_ShouldReturnFailure_IfExternalServiceFails()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            _mockExternalOrderService
                .Setup(s => s.RegisterOrderAsync(It.IsAny<Order>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(
                    Result<TabadulOrderRegisterResponse>.Failure(new Error(ErrorNumbers.TabadulError, "Something went wrong!", "Details"))
                );

            var request = CreateCommonRequest();
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsFailure.Should().BeTrue();
            result.Error.Code.Should().Contain("Something went wrong!");
            result.Error.Message.Should().Contain("Details");
            result.Data.Should().Be((null, null)!);
        }
        
        [Fact]
        public async Task RegisterOrderAsync_ShouldSucceed_AndUseExternalServiceResponse()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            var fakeOrderId = Guid.NewGuid();
            _mockExternalOrderService
                .Setup(s => s.RegisterOrderAsync(It.IsAny<Order>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(() =>
                {
                    var tabadulResponse = new TabadulOrderRegisterResponse
                    {
                        OrderId = fakeOrderId,
                        FormUrl = "https://example.com/fake-form-url"
                    };
                    return Result<TabadulOrderRegisterResponse>.Success(tabadulResponse);
                });

            var request = CreateCommonRequest();
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            var (order, url) = result.Data;

            url.Should().Be("https://example.com/fake-form-url");
            order.Should().NotBeNull();
            order.Id.Value.Should().Be(fakeOrderId);
            order.CreatedAt.Should().BeCloseTo(DateTimeOffset.UtcNow, TimeSpan.FromSeconds(5));
            order.SecureSuccessToken.Should().NotBe(default);
            order.SecureFailureToken.Should().NotBe(default);
        }

        [Fact]
        public async Task RegisterOrderAsync_ShouldUseDefaultCustomerId_IfNoneProvided()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            var request = CreateCommonRequest(customerId: null);
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            
            result.IsSuccess.Should().BeTrue();
            result.Data.order.CustomerId.Value.Should().Be(AppConstants.DefaultCustomerId);
        }
        
        [Theory]
        [InlineData("CUSTOMER-XYZ")]
        [InlineData("ANOTHER-ONE-123")]
        public async Task RegisterOrderAsync_ShouldUseProvidedCustomerId_IfGiven(string providedCustomerId)
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            var request = CreateCommonRequest(customerId: providedCustomerId);
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Data.order.CustomerId.Value.Should().Be(providedCustomerId);
        }
        
        [Theory]
        [InlineData(-10)]
        [InlineData(0)]
        public async Task RegisterOrderAsync_ShouldUseDefaultExpiresInSeconds_IfZeroOrNegative( int expiresIn)  
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

          
            // Negative
            var request = CreateCommonRequest(expiresIn: expiresIn);
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);
            result.IsSuccess.Should().BeTrue();
            result.Data.order.ExpiresInSeconds.Should().Be(_orderConfiguration.ExpiresInSeconds);
        }

        [Fact]
        public async Task RegisterOrderAsync_ShouldUseProvidedExpiresInSeconds_IfPositive()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            var request = CreateCommonRequest(expiresIn: 999);
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Data.order.ExpiresInSeconds.Should().Be(999);
        }
        
        [Fact]
        public async Task RegisterOrderAsync_ShouldGenerateExternalId_IfNotProvided()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            var request = CreateCommonRequest(externalId: null);
            _mockMapper
                .Setup(m => m.Map<Order>(It.IsAny<OrderRegisterBaseRequest>()))
                .Returns(() => new Order
                {
                    Id = OrderId.From(Guid.NewGuid()),
                    Amount = 10,
                    Currency = CurrencyIso4217.USD,
                    SecureSuccessToken = default,
                    SecureFailureToken = default,
                });
            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            var generated = result.Data.order.ExternalId.Value;
            generated.Should().NotBeNullOrWhiteSpace();
            generated.Should().NotBe(_defaultOrder.ExternalId.Value, 
            "the mapper won't overwrite if we pass in null, so we expect a new random value.");
        }

       
        [Fact]
        public async Task RegisterOrderAsync_ShouldUseProvidedExternalId_IfGiven()
        {
            var service = InitializeService_MerchantConfig_AndPreventDuplicates();

            const string providedExternalId = "EXTID-ABC123";
            var request = CreateCommonRequest(externalId: providedExternalId);

            var result = await service.RegisterOrderAsync(request, saveChanges: true, null);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Data.order.ExternalId.Value.Should().Be(providedExternalId);
        }
        private static OrderRegisterBaseRequest CreateCommonRequest(
            string? externalId = null,
            long amount = 100,
            CurrencyIso4217 currency = CurrencyIso4217.IQD,
            string? customerId = null,
            string? phone = null,
            string? email = null,
            int expiresIn = 0)
        {
            return new OrderRegisterBaseRequest(externalId, amount, currency, customerId, phone, email, expiresIn);
        }
    }
}