// using FluentValidation.TestHelper;
// using FluentValidation.Validators;
// using PaymentGateway.WebApi.Features.Orders.Shared;
// using PaymentGateway.WebApi.Features.Payments.Cards.Shared;
// using PaymentGateway.WebApi.Features.Payments.OrderPay;
//
// namespace WebApi.UnitTests.Payments.OrderPay;
//
// public sealed class OrderPayRequestValidatorTests
// {
//     private readonly OrderPayRequestValidator _validator = new();
//
//     [Fact]
//     public void Should_Have_Error_When_Id_Is_Empty()
//     {
//         // Arrange
//         var request = new OrderPayRequest(Guid.Empty, new CardModel("****************", "2025", "12", "<PERSON>"), "123");
//
//         // Act
//         var result = _validator.TestValidate(request);
//
//         var notEmptyValidator = new NotEmptyValidator<OrderPayRequest, Guid>();
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.Id)
//             .WithErrorCode(notEmptyValidator.Name);
//     }
//
//     [Fact]
//     public void Should_Not_Have_Error_When_Id_Is_Not_Empty()
//     {
//         // Arrange
//         var request = new OrderPayRequest(Guid.NewGuid(), new CardModel("****************", "2025", "12", "John Doe"), "123");
//
//         // Act
//         var result = _validator.TestValidate(request);
//
//         // Assert
//         result.ShouldNotHaveValidationErrorFor(x => x.Id);
//     }
//
//     [Theory]
//     [InlineData("")]
//     [InlineData(" ")]
//     [InlineData(null)]
//     [InlineData("1")]
//     [InlineData("11")]
//     [InlineData("1111")]
//     [InlineData("a11")]
//     public void Should_Have_Error_When_Cvv_Is_Null(string cvv)
//     {
//         // Arrange
//         var request = new OrderPayRequest(Guid.NewGuid(), new CardModel("****************", "2025", "12", "John Doe"), cvv);
//
//         // Act
//         var result = _validator.TestValidate(request);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.Cvv)
//             .WithErrorCode(OrderErrors.CvvShouldBe3Digits.Code)
//             .WithErrorMessage(OrderErrors.CvvShouldBe3Digits.Message);
//     }
//
//
//     [Fact]
//     public void Should_Not_Have_Error_When_Cvv_Is_Valid()
//     {
//         // Arrange
//         var request = new OrderPayRequest(Guid.NewGuid(), new CardModel("****************", "2025", "12", "John Doe"), "123");
//
//         // Act
//         var result = _validator.TestValidate(request);
//
//         // Assert
//         result.ShouldNotHaveValidationErrorFor(x => x.Cvv);
//     }
// }