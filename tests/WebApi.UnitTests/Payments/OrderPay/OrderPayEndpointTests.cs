// using System.Linq.Expressions;
// using FastEndpoints;
// using FluentAssertions;
// using Microsoft.AspNetCore.Http;
// using Microsoft.Extensions.Logging;
// using Moq;
// using PaymentGateway.Integrations.Services.Tabadul;
// using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;
// using PaymentGateway.WebApi.Common;
// using PaymentGateway.WebApi.Common.Services;
// using PaymentGateway.WebApi.Common.Services.Hangfire;
// using PaymentGateway.WebApi.Features.Merchants.Data;
// using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Orders.OrderFind;
// using PaymentGateway.WebApi.Features.Orders.Services;
// using PaymentGateway.WebApi.Features.Orders.Shared;
// using PaymentGateway.WebApi.Features.Payments.Cards.Data;
// using PaymentGateway.WebApi.Features.Payments.Cards.Shared;
// using PaymentGateway.WebApi.Features.Payments.OrderPay;
// using PaymentGateway.WebApi.Features.Payments.Services;
// using WebApi.UnitTests.Common.Extensions;
// using IMapper = AutoMapper.IMapper;
// using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;
//
// namespace WebApi.UnitTests.Payments.OrderPay;
//
// public class OrderPayEndpointTests
// {
//     private readonly Mock<ICardRepository> _mockCardRepository = new();
//     private readonly Mock<IClickhouseTaskBuilder> _mockClickhouseTaskBuilder = new();
//     private readonly Mock<ITabadulOrderService> _mockClient = new();
//     private readonly Mock<IIpResolver> _mockIpResolver = new();
//     private readonly Mock<IHangfireJobQueueService> _mockJobQueueService = new();
//     private readonly Mock<ILogger<OrderPayEndpoint>> _mockLogger = new();
//     private readonly Mock<IMapper> _mockMapper = new();
//     private readonly Mock<IOrderEventRepository> _mockOrderEventRepository = new();
//     private readonly Mock<IOrderRepository> _mockOrderRepository = new();
//     private readonly Mock<IOrderUpserter> _mockOrderUpserter = new();
//     private readonly Mock<IRequestIdResolver> _mockRequestIdResolver = new();
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturnBadRequest_WhenOrderIdIsEmpty()
//     {
//         // Arrange
//         var endpoint = EndpointSetup();
//         var request = new OrderPayRequest(Guid.Empty, new CardModel("****************", "2025", "12", "Name"), "123");
//
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//         endpoint.HttpContext
//             .Response
//             .StatusCode
//             .Should()
//             .Be(StatusCodes.Status400BadRequest);
//
//         _mockOrderRepository.Verify(r => r.FindAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockClient.Verify(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockOrderUpserter.Verify(u => u.AddFailurePaymentEventAsync(It.IsAny<Guid>(), It.IsAny<Error>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
//             Times.Never);
//         _mockCardRepository.Verify(repo => repo.UpsertCard(It.IsAny<OrderPayRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockJobQueueService.Verify(j => j.Enqueue(It.IsAny<Expression<Action<IMerchantNotifier>>>()), Times.Never);
//     }
//
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturnNotFound_WhenOrderDoesNotExist()
//     {
//         // Arrange
//         var endpoint = EndpointSetup();
//         var request = new OrderPayRequest(Guid.NewGuid(), new CardModel("****************", "2025", "12", "Name"), "123");
//
//         _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync((Order)null!);
//
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//         endpoint.AssetErrorResponse(OrderErrors.OrderNotFound.StatusCode);
//
//         _mockOrderRepository.Verify(repo => repo.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()), Times.Once);
//         _mockClient.Verify(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockOrderUpserter.Verify(
//             u => u.AddSuccessPaymentEventAsync(It.IsAny<Guid>(), It.IsAny<TabadulOrderPaymentResponse>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
//             Times.Never);
//         _mockCardRepository.Verify(repo => repo.UpsertCard(request, It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockJobQueueService.Verify(j => j.Enqueue(It.IsAny<Expression<Action<IMerchantNotifier>>>()), Times.Never);
//     }
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturnBadRequest_WhenOrderIsAlreadyPaid()
//     {
//         // Arrange
//         var endpoint = EndpointSetup();
//         var orderId = Guid.NewGuid();
//         var request = new OrderPayRequest(orderId, new CardModel("****************", "2025", "12", "Name"), "123");
//
//         _mockOrderRepository.Setup(x => x.FindAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(new OrdersDetailsResponse
//             {
//                 Id = orderId,
//                 OrderStatus = OrderStatuses.Successful,
//                 MerchantId = "MRC001",
//                 Currency = 0,
//                 CreatedAt = default,
//             });
//
//         _mockOrderEventRepository.Setup(x => x.OrderIsClosedAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(true);
//
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//         await endpoint.AssertBadRequest(OrderErrors.OrderIsAlreadyPaid);
//
//         _mockClient.Verify(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockOrderUpserter.Verify(
//             u => u.AddSuccessPaymentEventAsync(It.IsAny<Guid>(), It.IsAny<TabadulOrderPaymentResponse>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
//             Times.Never);
//         _mockCardRepository.Verify(repo => repo.UpsertCard(request, It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
//         _mockJobQueueService.Verify(j => j.Enqueue(It.IsAny<Expression<Action<IMerchantNotifier>>>()), Times.Never);
//     }
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturnBadRequest_WhenTabadulPaymentFails()
//     {
//         // Arrange
//         var endpoint = EndpointSetup();
//         var orderId = Guid.NewGuid();
//         var request = new OrderPayRequest(orderId, new CardModel("****************", "2025", "12", "Name"), "123");
//
//         _mockClient.Setup(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(Result<TabadulOrderPaymentResponse>.Failure(new Error(1, "Payment failed", "")));
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//
//         endpoint.HttpContext
//             .Response
//             .Should()
//             .NotBeNull();
//         endpoint.HttpContext
//             .Response
//             .StatusCode
//             .Should()
//             .Be(StatusCodes.Status400BadRequest);
//
//         _mockOrderUpserter.Verify(u => u.AddFailurePaymentEventAsync(It.IsAny<Guid>(), It.IsAny<Error>(), It.IsAny<string>(), It.IsAny<string>(), true, It.IsAny<CancellationToken>()), Times.Once);
//
//         _mockJobQueueService.Verify(j => j.Enqueue(It.IsAny<Expression<Action<IMerchantNotifier>>>()), Times.Once);
//
//         _mockOrderUpserter.Verify(
//             u => u.AddSuccessPaymentEventAsync(It.IsAny<Guid>(), It.IsAny<TabadulOrderPaymentResponse>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()),
//             Times.Never);
//         _mockCardRepository.Verify(repo => repo.UpsertCard(It.IsAny<OrderPayRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
//     }
//
//     [Fact]
//     public async Task HandleAsync_ShouldReturnOkResult_ForValidRequest()
//     {
//         // Arrange
//         var endpoint = EndpointSetup();
//         var orderId = Guid.NewGuid();
//         var request = new OrderPayRequest(orderId, new CardModel("****************", "2025", "12", "Name"), "123");
//         _mockClient.Setup(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(Result<TabadulOrderPaymentResponse>.Success(new TabadulOrderPaymentResponse
//             {
//                 Redirect = null,
//                 Info = null,
//                 ErrorCode = (int)TabadulErrorCode.Success,
//                 ErrorMessage = null,
//                 TransactionDataElements = null,
//                 OrderStatusCode = TabadulOrderStatuses.DepositedSuccessfully,
//                 OrderStatusName = null,
//             }));
//
//         // Act
//         await endpoint.HandleAsync(request, CancellationToken.None);
//
//         // Assert
//
//         _mockOrderRepository.Verify(repo => repo.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()), Times.Once);
//         _mockClient.Verify(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()), Times.Once);
//         _mockOrderUpserter.Verify(
//             u => u.AddSuccessPaymentEventAsync(It.IsAny<Guid>(), It.IsAny<TabadulOrderPaymentResponse>(), It.IsAny<string>(), It.IsAny<string>(), true, It.IsAny<CancellationToken>()), Times.Once);
//         _mockCardRepository.Verify(repo => repo.UpsertCard(request, It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
//
//         _mockJobQueueService.Verify(j => j.Enqueue(It.IsAny<Expression<Action<IMerchantNotifier>>>()), Times.Once);
//
//         var response = endpoint.Response;
//         response.Should()
//             .NotBeNull();
//         response.Should()
//             .BeOfType<OrderPayResponse>();
//         response.OrderStatus
//             .Should()
//             .Be(OrderStatuses.Successful);
//         response.TransactionDataElements
//             .Should()
//             .NotBeNullOrEmpty();
//         response.Info
//             .Should()
//             .NotBeNullOrEmpty();
//     }
//
//
//     private OrderPayEndpoint EndpointSetup()
//     {
//         var existingOrdersDetailsResponse = new OrdersDetailsResponse
//         {
//             Id = Guid.NewGuid(),
//             MerchantId = "MRC001",
//             ExternalId = Ulid.NewUlid()
//                 .ToString(),
//             Amount = 10,
//             Currency = CurrencyIso4217.USD,
//             ExpiresInSeconds = 600,
//             CustomerId = "Customer-001",
//             CreatedAt = DateTimeOffset.UtcNow,
//             OrderStatus = OrderStatuses.Initiated,
//         };
//         var existingOrder = new Order
//         {
//             Id = OrderId.From(Guid.NewGuid()),
//             MerchantId = MerchantId.From("MRC001"),
//             ExternalId = ExternalId.From(Ulid.NewUlid()
//                 .ToString()),
//             Amount = 10,
//             Currency = CurrencyIso4217.USD,
//             ExpiresInSeconds = 600,
//             CustomerId = CustomerId.From("Customer-001"),
//             CreatedAt = DateTimeOffset.UtcNow,
//             SecureSuccessToken = Ulid.NewUlid(),
//             SecureFailureToken = Ulid.NewUlid(),
//         };
//
//
//         _mockClickhouseTaskBuilder.Setup(x => x.SendToClickhouse(It.IsAny<OrderPayRequest>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Order>(), It.IsAny<CancellationToken>()));
//
//         _mockOrderRepository.Setup(m => m.FindAsync(It.IsAny<OrderId>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(existingOrdersDetailsResponse);
//         _mockOrderRepository.Setup(m => m.FindAsync(It.IsAny<Expression<Func<Order, bool>>>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(existingOrder);
//
//         _mockClient.Setup(c => c.PayToTabadulAsync(It.IsAny<OrderPayRequest>(), It.IsAny<CancellationToken>()))
//             .ReturnsAsync(Result<TabadulOrderPaymentResponse>.Success(new TabadulOrderPaymentResponse
//             {
//                 Redirect = null,
//                 Info = null,
//                 ErrorCode = (int)TabadulErrorCode.Success,
//                 ErrorMessage = null,
//                 TransactionDataElements = null,
//                 OrderStatusCode = TabadulOrderStatuses.OrderRegistered,
//                 OrderStatusName = null,
//             }));
//
//         _mockOrderUpserter.Setup(u => u.AddSuccessPaymentEventAsync(It.IsAny<Guid>(), It.IsAny<TabadulOrderPaymentResponse>(), It.IsAny<string>(), It.IsAny<string>(), bool.TrueString == "true",
//                 It.IsAny<CancellationToken>()))
//             .Returns(Task.CompletedTask);
//
//         _mockCardRepository.Setup(repo => repo.UpsertCard(It.IsAny<OrderPayRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
//             .Returns(Task.CompletedTask);
//
//         _mockMapper.Setup(m => m.Map<OrderPayResponse>(It.IsAny<TabadulOrderPaymentResponse>()))
//             .Returns(new OrderPayResponse
//             {
//                 OrderStatus = OrderStatuses.Successful,
//                 TransactionDataElements = "566656566776869",
//                 Info = "Payment successful",
//             });
//
//
//         _mockIpResolver.Setup(ip => ip.GetIp())
//             .Returns("127.0.0.1");
//
//         _mockRequestIdResolver.Setup(r => r.GetRequestId())
//             .Returns("test-request-id");
//
//         var endpoint = Factory.Create<OrderPayEndpoint>(_mockClickhouseTaskBuilder.Object, _mockOrderRepository.Object, _mockLogger.Object, _mockOrderEventRepository.Object, _mockClient.Object,
//             _mockCardRepository.Object, _mockOrderUpserter.Object, _mockJobQueueService.Object, _mockMapper.Object, _mockIpResolver.Object, _mockRequestIdResolver.Object);
//
//         endpoint.HttpContext.Response.Body = new MemoryStream();
//         return endpoint;
//     }
// }
