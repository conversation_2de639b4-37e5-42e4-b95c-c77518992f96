// using FluentValidation.TestHelper;
// using PaymentGateway.WebApi.Features.Payments.Cards.Shared;
//
// namespace WebApi.UnitTests.Payments.Cards;
//
// public sealed class CardModelValidatorTests
// {
//     private readonly CardModelValidator _validator = new();
//
//     // Tests for CardNumber field
//     [Fact]
//     public void ShouldNotHaveError_When_CardNumberIsValid()
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", "12", "<PERSON>");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldNotHaveValidationErrorFor(x => x.CardNumber);
//     }
//
//     [Theory]
//     [InlineData("123456789012345")]
//     [InlineData("12345678901234567")]
//     [InlineData("123456789012345a")]
//     [InlineData("a234567890123456")]
//     [InlineData(" ")]
//     [InlineData("")]
//     public void TestingCardNumberValidation(string cardNumber)
//     {
//         // Arrange
//         var card = new CardModel(cardNumber, "2025", "12", "John Doe");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.CardNumber)
//             .WithErrorCode(CardErrors.CardNumberBe16Digits.Code)
//             .WithErrorMessage(CardErrors.CardNumberBe16Digits.Message);
//     }
//
//
//     [Fact]
//     public void ShouldNotHaveError_When_YearOfExpiryIsValid()
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", "12", "John Doe");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldNotHaveValidationErrorFor(x => x.YearOfExpiry);
//     }
//
//     [Theory]
//     [InlineData("1")]
//     [InlineData("11")]
//     [InlineData("111")]
//     [InlineData("")]
//     [InlineData(" ")]
//     public void Testing_YearOfExpiry_WhenItHasLessThan4Digits(string year)
//     {
//         // Arrange
//         var card = new CardModel("****************", year, "12", "John Doe");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.YearOfExpiry)
//             .WithErrorCode(CardErrors.YearOfExpiryShouldBe4Digits.Code)
//             .WithErrorMessage(CardErrors.YearOfExpiryShouldBe4Digits.Message);
//     }
//     
//
//
//     [Theory]
//     [InlineData("2076")]
//     [InlineData("2024")]
//     public void Testing_YearOfExpiry_WhenItIsNotBetweenCurrentYearAndNext50(string year)
//     {
//         // Arrange
//         var card = new CardModel("****************", year, "12", "John Doe");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.YearOfExpiry)
//             .WithErrorCode(CardErrors.YearOfExpiryShouldBeBetweenCurrentYearAndNext50.Code)
//             .WithErrorMessage(CardErrors.YearOfExpiryShouldBeBetweenCurrentYearAndNext50.Message);
//     }
//
//     [Fact]
//     public void ShouldNotHaveError_When_MonthOfExpiryIsValid()
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", "06", "John Doe");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldNotHaveValidationErrorFor(x => x.MonthOfExpiry);
//     }
//
//     [Theory]
//     [InlineData("1")]
//     [InlineData("")]
//     [InlineData(" ")]
//     public void ShouldHaveError_When_MonthOfExpiryHasLessThan2Digits(string month)
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", month, "John Doe");
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.MonthOfExpiry)
//             .WithErrorCode(CardErrors.MonthOfExpiryShouldBe2Digits.Code)
//             .WithErrorMessage(CardErrors.MonthOfExpiryShouldBe2Digits.Message);
//     }
//     
//
//     [Theory]
//     [InlineData("00")]
//     [InlineData("14")]
//     public void ShouldHaveError_When_MonthOfExpiryIsOutOfRange(string month)
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", month, "John Doe"); 
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.MonthOfExpiry)
//             .WithErrorCode(CardErrors.MonthOfExpiryShouldBeBetween1And12.Code)
//             .WithErrorMessage(CardErrors.MonthOfExpiryShouldBeBetween1And12.Message);
//     }
//
//     [Theory]
//     [InlineData("")]
//     [InlineData(null)]
//     public void ShouldNotHaveError_When_CardHolderNameIsNullOrEmpty(string? name)
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", "12", name);
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldNotHaveValidationErrorFor(x => x.CardHolderName);
//     }
//     [Fact]
//     public void ShouldHaveError_When_CardHolderNameExceedsMaxLength()
//     {
//         // Arrange
//         var longName = new string('A', 27); // 27 characters
//         var card = new CardModel("****************", "2025", "12", longName);
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.CardHolderName)
//             .WithErrorCode(CardErrors.CardHolderNameShouldBeLessThan27Characters.Code)
//             .WithErrorMessage(CardErrors.CardHolderNameShouldBeLessThan27Characters.Message);
//     }
//
//     [Theory]
//     [InlineData("John@Doe!")]
//     [InlineData(" ")]
//     [InlineData("1Ahmed Ali")]
//     public void ShouldHaveError_When_CardHolderNameHasInvalidCharacters(string invalidName)
//     {
//         // Arrange
//         var card = new CardModel("****************", "2025", "12", invalidName);
//
//         // Act
//         var result = _validator.TestValidate(card);
//
//         // Assert
//         result.ShouldHaveValidationErrorFor(x => x.CardHolderName)
//             .WithErrorCode(CardErrors.CardHolderNameShouldValidName.Code)
//             .WithErrorMessage(CardErrors.CardHolderNameShouldValidName.Message);
//     }
// }
