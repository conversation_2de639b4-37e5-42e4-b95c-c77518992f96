// using AutoMapper;
// using FluentAssertions;
// using Microsoft.EntityFrameworkCore;
// using Moq;
// using PaymentGateway.WebApi.Data;
// using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Payments.Cards.Data;
// using PaymentGateway.WebApi.Features.Payments.Cards.Shared;
// using PaymentGateway.WebApi.Features.Payments.OrderPay;
//
// namespace WebApi.UnitTests.Payments.Cards.Data;
//
// public class CardRepositoryTests : IDisposable
// {
//     private readonly PaymentDbContext _context;
//     private readonly CardRepository _repository;
//
//     public CardRepositoryTests()
//     {
//         var options = new DbContextOptionsBuilder<PaymentDbContext>().UseInMemoryDatabase(databaseName: Guid.NewGuid()
//                 .ToString())
//             .Options;
//
//         _context = new PaymentDbContext(options);
//         _context.Database.EnsureCreated();
//         Mock<IMapper> mapper = new();
//         mapper.Setup(x => x.Map<Card>(It.IsAny<CardModel>()))
//             .Returns((CardModel model) => new Card
//             {
//                 CardNumber = model.CardNumber,
//                 CardHolderName = model.CardHolderName,
//                 YearOfExpiry = model.YearOfExpiry,
//                 MonthOfExpiry = model.MonthOfExpiry,
//                 CustomerId = CustomerId.From("cus-1"),
//                 Id = CardId.New(),
//                 IsActive = true
//             });
//         _repository = new CardRepository(_context, mapper.Object);
//     }
//
//     public void Dispose()
//     {
//         _context.Database.EnsureDeleted();
//         _context.Dispose();
//     }
//
//     [Fact]
//     public async Task UpsertCard_ShouldUpdateCard_When_CardExists()
//     {
//         // Arrange
//         var customerId = Guid.NewGuid()
//             .ToString();
//         var request = new OrderPayRequest(Guid.NewGuid(), new CardModel("****************", "2025", "12", "Name"), "123");
//
//         var card = new Card
//         {
//             Id = CardId.New(),
//             CardNumber = request.Card.CardNumber,
//             CardHolderName = "Old Card Holder Name",
//             YearOfExpiry = "old year",
//             MonthOfExpiry = "old month",
//             CustomerId = CustomerId.From(customerId),
//         };
//
//         _context.Cards.Add(card);
//         await _context.SaveChangesAsync();
//
//         // Act
//         await _repository.UpsertCard(request, customerId, CancellationToken.None);
//
//         // Assert
//         var updatedCard = await _context.Cards.FindAsync(card.Id);
//         updatedCard.Should()
//             .NotBeNull();
//         updatedCard!.CardHolderName
//             .Should()
//             .Be(request.Card.CardHolderName);
//         updatedCard.YearOfExpiry
//             .Should()
//             .Be(request.Card.YearOfExpiry);
//         updatedCard.MonthOfExpiry
//             .Should()
//             .Be(request.Card.MonthOfExpiry);
//     }
//
//     [Fact]
//     public async Task UpsertCard_ShouldAddCard_When_CardDoesNotExist()
//     {
//         // Arrange
//         var customerId = Guid.NewGuid()
//             .ToString();
//         var request = new OrderPayRequest(Guid.NewGuid(), new CardModel("****************", "2025", "12", "Name"), "123");
//
//         // Act
//         await _repository.UpsertCard(request, customerId, CancellationToken.None);
//
//         // Assert
//         var card = await _context.Cards.FirstOrDefaultAsync(x => x.CardNumber == request.Card.CardNumber);
//         card.Should()
//             .NotBeNull();
//         card!.CardHolderName
//             .Should()
//             .Be(request.Card.CardHolderName);
//         card.YearOfExpiry
//             .Should()
//             .Be(request.Card.YearOfExpiry);
//         card.MonthOfExpiry
//             .Should()
//             .Be(request.Card.MonthOfExpiry);
//     }
// }
