using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Merchants.Data;

namespace WebApi.UnitTests.Data;

public class RepositoryMerchants : IDisposable
{
    private readonly PaymentDbContext _context;
    private readonly Repository<Merchant, MerchantId> _repository;

    public RepositoryMerchants()
    {
        var options = new DbContextOptionsBuilder<PaymentDbContext>().UseInMemoryDatabase(Guid.NewGuid()
                .ToString())
            .Options;

        _context = new PaymentDbContext(options);
        _context.Database.EnsureCreated();

        _repository = new Repository<Merchant, MerchantId>(_context);
    }

    public void Dispose()
    {
        _context.Database.EnsureDeleted();
        _context.Dispose();
    }

    [Fact]
    public async Task FindAsync_ShouldReturnEntity_When_EntityExists()
    {
        // Arrange
        var entity = new Merchant { Id = MerchantId.From("1"), Name = "Merchant Name", };
        _context.Set<Merchant>()
            .Add(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindAsync(entity.Id);

        // Assert
        result.Should()
            .NotBeNull();
        result.Should()
            .BeEquivalentTo(entity);
    }

    [Fact]
    public async Task FindAsync_ShouldReturnNull_When_EntityDoesNotExist()
    {
        // Arrange
        var id = MerchantId.From("1");

        // Act
        var result = await _repository.FindAsync(id);

        // Assert
        result.Should()
            .BeNull();
    }

    [Fact]
    public async Task FindAsync_ShouldReturnEntity_When_EntityExistsWithPredicate()
    {
        // Arrange
        var entity = new Merchant { Id = MerchantId.From("1"), Name = "Merchant Name", };
        _context.Set<Merchant>()
            .Add(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.FindAsync(e => e.Id == entity.Id, CancellationToken.None);

        // Assert
        result.Should()
            .NotBeNull();
        result.Should()
            .BeEquivalentTo(entity);
    }

    [Fact]
    public async Task FindAsync_ShouldReturnNull_When_EntityDoesNotExistWithPredicate()
    {
        // Arrange
        var id = MerchantId.From("1");

        // Act
        var result = await _repository.FindAsync(e => e.Id == id, CancellationToken.None);

        // Assert
        result.Should()
            .BeNull();
    }

    [Fact]
    public async Task AddAsync_ShouldAddEntity_When_EntityIsValid()
    {
        // Arrange
        var entity = new Merchant { Id = MerchantId.From("1"), Name = "Merchant Name", };

        // Act
        var result = await _repository.AddAsync(entity);

        // Assert
        result.Should()
            .NotBeNull();
        result.Should()
            .BeEquivalentTo(entity);
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_When_EntityIsValid()
    {
        // Arrange
        var entity = new Merchant { Id = MerchantId.From("1"), Name = "Merchant Name", };
        _context.Set<Merchant>()
            .Add(entity);
        await _context.SaveChangesAsync();

        // Act
        entity.Name = "Updated Name";
        var result = await _repository.UpdateAsync(entity);

        // Assert
        result.Should()
            .NotBeNull();
        result.Should()
            .BeEquivalentTo(entity);
    }

    [Fact]
    public void GetQueryable_ShouldReturnQueryable()
    {
        // Act
        var result = _repository.GetQueryable();

        // Assert
        result.Should()
            .NotBeNull();
        result.Should()
            .BeAssignableTo<IQueryable<Merchant>>();
    }

    [Fact]
    public async Task SaveChangesAsync_ShouldSaveChanges()
    {
        // Arrange
        var entity = new Merchant { Id = MerchantId.From("1"), Name = "Merchant Name", };
        _context.Set<Merchant>()
            .Add(entity);

        // Act
        await _repository.SaveChangesAsync(CancellationToken.None);

        // Assert
        _context.Set<Merchant>()
            .Count()
            .Should()
            .Be(1);
    }
    
    [Fact]
    public async Task FindAsync_ById_ShouldReturnNull_WhenNotExists()
    {
        // Arrange
        var options = new DbContextOptionsBuilder<PaymentDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        await using var context = new PaymentDbContext(options);
        var repository = new Repository<Merchant, MerchantId>(context);

        var testId = MerchantId.From("1");

        // Act
        var result = await repository.FindAsync(testId);

        // Assert
        result.Should().BeNull();
    }
    
    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity_WithoutSaving()
    {
        // Arrange
        var options = new DbContextOptionsBuilder<PaymentDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;

        await using var context = new PaymentDbContext(options);
        var repository = new Repository<Merchant, MerchantId>(context);

        var testId = MerchantId.From("1");
        var testEntity = new Merchant
        {
            Id = testId, Name = "Merchant Name",
        };

        context.Set<Merchant>().Add(testEntity);
        await context.SaveChangesAsync();

        // Act
        await repository.UpdateAsync(testEntity, false, CancellationToken.None);

        // Assert
        var updatedEntity = await context.Set<Merchant>().FindAsync(testId);
        updatedEntity.Should().BeEquivalentTo(testEntity);
    }
    
    [Fact]
    public async Task AddAsync_ShouldAddEntity_WithoutSaving()
    {
        // Arrange
        var entity = new Merchant { Id = MerchantId.From("1"), Name = "Merchant Name" };

        // Act
        var result = await _repository.AddAsync(entity, save: false);

        // Assert
        result.Should().NotBeNull();
        _context.Set<Merchant>().Count().Should().Be(0); // Ensure changes are not saved
    }

    [Fact]
    public async Task AddAsync_ShouldThrowException_WhenEntityIsNull()
    {
        // Arrange
        Merchant entity = null;

        // Act
        Func<Task> act = async () => await _repository.AddAsync(entity);

        // Assert
        await act.Should().ThrowAsync<ArgumentNullException>();
    }
}
