variables:
  Unit_Testing: "true"
  K8S_API_STG_PRD: 'https://api.rhocp-prod2.elcld.net:6443'
  K8S_NAMESPACE_STG: 'payment-gateway-stg'
  K8S_NAMESPACE_PRD: 'payment-gateway-prd'
  K8S_APPNAME: 'dotnet'
  Dockerfile_Path: 'docker/Dockerfile'
  Dockerfile_Root_Dir: '.'
  TAG_PREFIX: 'pgw/'
  TAG_PREFIX_RULE_REGEX: "/^pgw\/.*$/"
  PM_MEMBER_TEAM: '<EMAIL>,<EMAIL>,<EMAIL>,' # add email of PM Member team example: "<EMAIL>,<EMAIL>,"
  Deploy_Strategy: 'image'
  Appsonar_infosec_email: 'false'

  ##### For DEV ENV
  Develop: 'true' # (Required) add true if need to manage Dev env
  K8S_API_DEV_TEST: 'https://api.rhocp-test-dev.elcld.net:6443' # (Required) add k8s ckuster api url like (https://api.rhocp-test-dev.elcld.net:6443)
  K8S_NAMESPACE_DEV: 'payment-gateway-dev' # (Required) add namespace of prod env in k8s cluster
  K8S_APPNAME_DEV: '$K8S_APPNAME' # (Required) add applection name or imagestream name in k8s cluster
  Dockerfile_DEV_Path: '$Dockerfile_Path' # (Required) dockerfile path in repo
  Dockerfile_DEV_Root_Dir: '$Dockerfile_Root_Dir' # (Required) docker build context directory

  ##### For Testing ENV
  Testing: 'true' # (Required) add true if need to manage Dev env
  K8S_NAMESPACE_TEST: 'payment-gateway-test' # (Required) add namespace of prod env in k8s cluster
  K8S_APPNAME_TEST: '$K8S_APPNAME' # (Required) add applection name or imagestream name in k8s cluster
  Dockerfile_TEST_Path: '$Dockerfile_Path' # (Required) dockerfile path in repo
  Dockerfile_TEST_Root_Dir: '$Dockerfile_Root_Dir' # (Required) docker build context directory

  ##### For RC ENV
  RC: 'false' # (Required) add true if need to manage RC env
  K8S_NAMESPACE_RC: 'payment-gateway-rc' # (Required) add namespace of RC env in k8s cluster
  K8S_APPNAME_RC: '$K8S_APPNAME' # (Required) add applection name or imagestream name in k8s cluster
  Dockerfile_RC_Path: '$Dockerfile_Path' # (Required) dockerfile path in repo
  Dockerfile_RC_Root_Dir: '$Dockerfile_Root_Dir' # (Required) docker build context directory
  TAG_PREFIX_RC: 'frontend-rc/' # (Required) add tag prefix that will trigger pipline example (portals/)
  TAG_PREFIX_RULE_REGEX_RC: "/^frontend-rc\/.*$/" # (Required) add Rule Regex of tag prefix that will trigger pipline example ("/^portals\/.*$/")
  Build_ARG_RC: '--build-arg env=rc' # (Optional) add build arg for RC if needed example (--build-arg "MY_VAR='value with spaces'"  --build-arg "MY_VAR2='value with spaces'")


include:
  - project: 'ci-cd-templates/deployment'
    ref: main
    file: /.gitlab-ci.yml

stages:
  - Build-and-Scan
  - Deploy-Stage
  - Testing-Stage
  - Create_Release
  - Deploy-Production
  - Testing-Production
  - Send-Email-Reply
