# Project PaymentGateway
#### This project’s structure is organized for clarity and scalability. The src directory separates external integrations from core WebApi features, which include data entities, configurations, repositories, endpoints, and shared resources. Testing is supported through Unit and optional Integration tests, ensuring a maintainable and well-tested application.
 * src
   * Integrations  ```contains the integrations with external services```
      * Clickhouse
      * Tabadul
   * WebApi
     * Features
          - [Feature]s 
            1. Data
               * [Feature].cs ``contains the entity that maps the database table``
               * [Feature]Configuration.cs "the database configuration for the entity which is iherited from ``IEntityTypeConfiguration``"
               * [Feature]Profile.cs ``contains the mapping profile for the entity which is inherited from ``Profile`` class``
               * [FeatureRepository.cs ``contains the repository for the entity``]
               * .. etc.
          
            2. Extensions
               * ``WebApplicationBuilderExtensions.cs`` which we can register the services in the DI container and register the configurations for this slice.
            3. [Feature]Add - with status response `201`.
               * [Feature]AddEndpoint.cs 
               * [Feature]AddRequest.cs
               * [Feature]AddRequestValidator.cs
            4. [Feature]Update  - with status response `200`.
            5. [Feature]Delete - with status response `204`.
            6. [Feature]Get ( returns list of records ) - with status response `200`.
            7. [Feature]Find (returns single record ) - with status response `200`.
            8. Shared
               * [Feature]Errors.cs ``contains the error messages for the entity``
               * ```contains shared files like FeatureResponse.cs which is used by multiple endpoints inside the slice```
     * Data
        * Migrations
          * 20210101000000_InitialCreate.cs
        * DbContext.cs
     * Common
        * Services
           * Contains the services and preferred to use this naming convention `IFileUpdater` instead of `IFileHelper` or `IFileService`
        * Middleware
          - contains all middleware
        * Extensions
           - extensions that used in more that one layer
 * tests
   * UnitTests
   * IntegrationTests ```if needed```
 
