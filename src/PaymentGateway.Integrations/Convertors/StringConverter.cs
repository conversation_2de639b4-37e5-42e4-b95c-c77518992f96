using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace PaymentGateway.Integrations.Convertors;

public class StringConverter : JsonConverter<string>
{
    public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        return reader.TokenType switch
        {
            JsonTokenType.Number => reader.GetDecimal()
                .ToString(CultureInfo.InvariantCulture),
            JsonTokenType.String => reader.GetString()!,
            _ => throw new JsonException($"Unexpected token type {reader.TokenType}")
        };
    }

    public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value);
    }
}
