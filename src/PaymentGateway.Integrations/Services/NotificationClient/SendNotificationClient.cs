using System.Net.Http.Json;
using Microsoft.Extensions.Logging;

namespace PaymentGateway.Integrations.Services.NotificationClient;

public interface INotificationSender
{
    Task<HttpResponseMessage> SendMessageAsync<TRequest>(string absoluteUrl, TRequest payload, CancellationToken cancellationToken = default) where TRequest : class;
}

public class NotificationSender(IHttpClientFactory factory, ILogger<NotificationSender> logger) : INotificationSender
{
    public async Task<HttpResponseMessage> SendMessageAsync<TRequest>(string absoluteUrl, TRequest payload, CancellationToken cancellationToken = default) where TRequest : class
    {
        using var httpClient = factory.CreateClient();
        logger.LogInformation("Sending request to {AbsoluteUrl}", absoluteUrl);
        var response = await httpClient.PostAsJsonAsync(absoluteUrl, payload, cancellationToken);
        return response;
    }
}
