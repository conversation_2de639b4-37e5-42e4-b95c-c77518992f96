using System.Text.Json;
using System.Text.Json.Serialization;
using PaymentGateway.Integrations.Convertors;
using PaymentGateway.Integrations.Extensions;
using PaymentGateway.Integrations.Services.Tabadul.OrderGetStatusModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;

namespace PaymentGateway.Integrations.Services.Tabadul;


internal sealed class TabadulClient(HttpClient httpClient,IMerchantCredentialProvider merchantCredentialProvider) : IClient
{
    public async Task<TResponse> SendAsync<TRequest, TResponse>(string route, TRequest model, CancellationToken cancellationToken) where TRequest : TabadulBaseRequest
    {

        var keyValuePairs = model.GetType()
            .GetProperties()
            .Where(prop => prop.GetValue(model) != null)
            .ToDictionary(prop => prop.Name.ToCamelCase(), prop => prop.GetValue(model)
                ?.ToString() ?? string.Empty);
        var content = new FormUrlEncodedContent(keyValuePairs);
        using var response = await httpClient.PostAsync(route, content, cancellationToken);
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            Converters =
            {
                new JsonStringEnumConverter(JsonNamingPolicy.CamelCase),
                new StringConverter(),
            },
        };
      

        var result = JsonSerializer.Deserialize<TResponse>(responseContent, options);
        return result!;
    }

    public async Task<OrderGetStatusResponse> GetStatus(OrderGetStatusRequest request, CancellationToken cancellationToken)
    {
       
        return await SendAsync<OrderGetStatusRequest,OrderGetStatusResponse>("epg/rest/getOrderStatus.do", request, cancellationToken);
       
    }
    // public async Task<TabadulOrderPaymentResponse> Pay(TabadulOrderPaymentRequest request, CancellationToken cancellationToken)
    // {
    //     Dictionary<string, string> keyValuePairs = new()
    //     {
    //         { "userName", request.UserName },
    //         { "password", request.Password },
    //         { "MDORDER", request.OrderId },
    //         { "$PAN", request.CardNumber },
    //         { "$CVC", request.Cvc },
    //         { "YYYY", request.YearOfExpiry },
    //         { "MM", request.MonthOfExpiry }
    //     };
    //
    //     var content = new FormUrlEncodedContent(keyValuePairs);
    //     using var response = await httpClient.PostAsync("epg/rest/paymentorder.do", content, cancellationToken);
    //     response.EnsureSuccessStatusCode();
    //     var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
    //
    //     var options = new JsonSerializerOptions
    //     {
    //         PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    //         PropertyNameCaseInsensitive = true,
    //         Converters =
    //         {
    //             new JsonStringEnumConverter(JsonNamingPolicy.CamelCase),
    //             new StringConverter()
    //         }
    //     };
    //
    //     var result = JsonSerializer.Deserialize<TabadulOrderPaymentResponse>(responseContent, options);
    //     return result;
    // }
}