using System.Text.Json.Serialization;

namespace PaymentGateway.Integrations.Services.Tabadul;
[JsonConverter(typeof(JsonStringEnumConverter))]

public enum TabadulOrderStatuses
{
    /// <summary> Order registered, but not paid. </summary>
    OrderRegistered,
    /// <summary>
    ///     Transaction has been approved (for a one-phase payment). Pre-authorization amount was put on hold (for a
    ///     two-phase payment).
    /// </summary>
    TransactionApprove,
    /// <summary> Amount was deposited successfully. </summary>
    DepositedSuccessfully,
    /// <summary> Authorization has been reversed </summary>
    AuthorizationReversed,
    /// <summary> Transaction has been refunded. </summary>
    TransactionRefunded,
    /// <summary> Authorization is declined. </summary>
    AuthorizationDeclined = 6,
}

[JsonConverter(typeof(JsonStringEnumConverter))]

public enum TabadulErrorCode
{
    Success = 0,
    ExpectedOrderIdOrOrderNumber=1,    
    OrderDeclinedCredentialsError=2,
    AccessDeniedOrUserMustChangePasswordOrOrderIdEmpty=5,
    UnregisteredOrder=6,
    SystemError=7,
}
