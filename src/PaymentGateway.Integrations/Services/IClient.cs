using PaymentGateway.Integrations.Services.Tabadul.OrderGetStatusModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;

namespace PaymentGateway.Integrations.Services;

public interface IClient
{
    Task<TResponse> SendAsync<TRequest, TResponse>(string route, TRequest model, CancellationToken cancellationToken) where TRequest : TabadulBaseRequest;

    // Task<TabadulOrderPaymentResponse> Pay(TabadulOrderPaymentRequest request, CancellationToken cancellationToken);
    Task<OrderGetStatusResponse> GetStatus(OrderGetStatusRequest request, CancellationToken cancellationToken);
}

public abstract record TabadulBaseRequest
{
    public required string UserName { get; init; }
    public required string Password { get; init; }
}
