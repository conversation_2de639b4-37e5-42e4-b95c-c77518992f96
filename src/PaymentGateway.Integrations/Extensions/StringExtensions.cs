namespace PaymentGateway.Integrations.Extensions;

public static class StringExtensions
{
    public static string ToCamelCase(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        return char.ToLowerInvariant(input[0]) + input[1..];
    }
    
    public static bool IsNullOrWhiteSpace(this string? input)
    {
        return string.IsNullOrWhiteSpace(input);
    }
    public static bool IsNotNullOrWhiteSpace(this string? input)
    {
        return !IsNullOrWhiteSpace(input);
    }
}
