using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PaymentGateway.Integrations.Services;
using PaymentGateway.Integrations.Services.NotificationClient;
using PaymentGateway.Integrations.Services.Tabadul;

namespace PaymentGateway.Integrations.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddTabadulClient(this IServiceCollection services, IConfiguration configuration)
    {
        var rootUrl = configuration["Integrations:Tabadul:RootUrl"];
        services.AddHttpClient<IClient, TabadulClient>(client => { client.BaseAddress = new Uri(rootUrl!); });
        services.AddScoped<INotificationSender, NotificationSender>();
        return services;
    }
}
