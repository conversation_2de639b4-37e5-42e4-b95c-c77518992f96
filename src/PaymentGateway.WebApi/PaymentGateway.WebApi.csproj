<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Exe</OutputType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FastEndpoints" Version="7.0.1" />
        <PackageReference Include="FastEndpoints.Swagger" Version="7.0.1" />
        <PackageReference Include="Hangfire" Version="1.8.21" />
        <PackageReference Include="Hangfire.PostgreSql" Version="1.20.12" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.7.2" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.4" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
        <PackageReference Include="Ulid" Version="1.4.1" />
        <PackageReference Include="Vogen" Version="8.0.1" />
        <PackageReference Include="AutoMapper" Version="15.0.1" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Octonica.ClickHouseClient" Version="3.1.3" />
        <PackageReference Include="Dapper" Version="2.1.66" />
        <PackageReference Include="Elastic.Serilog.Sinks" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
        <PackageReference Include="Serilog.Enrichers.CorrelationId" Version="3.0.1" />
        <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
        <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
        <PackageReference Include="Serilog.Sinks.Debug" Version="3.0.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.ConfluentKafka" Version="0.1.4" />


    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\PaymentGateway.Integrations\PaymentGateway.Integrations.csproj" />
    </ItemGroup>

</Project>
