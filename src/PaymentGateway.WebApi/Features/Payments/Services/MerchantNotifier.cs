using System.Text.Json;
using Hangfire.Server;
using PaymentGateway.Integrations.Services.NotificationClient;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Payments.Services;

public class MerchantNotifier(
    ILogger<MerchantNotifier> logger,
    INotificationSender client,
    IOrderEventRepository orderEventRepository,
    IMerchantConfigRepository merchantConfigRepository,
    IJwsCreator jwsCreator) : IMerchantNotifier
{
    public async Task ProcessOrderPaymentNotification(Guid orderId, OrderNotificationMessage message, CancellationToken cancellationToken, PerformContext? context = null)
    {
        var config = await merchantConfigRepository.FindByMerchantId(message.MerchantId, cancellationToken);
        if (config is null) return;
        var maxRetryCount = config.MaxNotificationRetries;
        var url = config.PublicAPIUrl;
        var retryCount = context?.GetJobParameter<int>("RetryCount") ?? 0;
        var merchantName = config.Name;

        // Check if this is a manual retry from Hangfire UI
        var isManualRetry = IsManualRetry(context);
        var maxRetryReached = retryCount == maxRetryCount && !isManualRetry;
        var successDetails = new EventDetails(Request: "", Response: "", Url: url);

        if (!maxRetryReached)
        {
            if (isManualRetry)
            {
                logger.LogInformation("Request {RequestId}: Manual retry from Hangfire UI for order {OrderId} and customer {CustomerId} (bypassing max retry limit of {MaxRetryCount})",
                    message.RequestId, orderId, message.CustomerId, maxRetryCount);
            }
            else
            {
                logger.LogInformation("Request {RequestId}: Retry count {RetryCount} for order {OrderId} and customer {CustomerId}",
                    message.RequestId, retryCount, orderId, message.CustomerId);
            }
        }
        else
        {
            logger.LogInformation("Request {RequestId}: Max retry count {MaxRetryCount} reached for merchant {MerchantId} Processing payment for order {OrderId} and customer {CustomerId}",
                message.RequestId, maxRetryCount, message.MerchantId, orderId, message.CustomerId);
            var orderEvent = CreateNotificationEvent(message, EventTypes.MerchNotifyFailed, AppConstants.OrderEventDescriptions.MerchantMaxNotificationTriesReached,
                successDetails with { Request = $"Maximum tries ({maxRetryCount}) reached!", });
            await orderEventRepository.AddAsync(orderEvent, true, cancellationToken);
            return;
        }

        var failedResponseLogged = false;
        try
        {
            var payload = new MerchantNotificationPayload(orderId, message.ExternalId, message.MerchantId, merchantName,
                [new StatusChange(message.OrderId, message.ExternalId, message.OrderStatus, DateTimeOffset.UtcNow),], retryCount);
            var jws = jwsCreator.CreateSignedJws(payload);
            var response = await client.SendMessageAsync(url, jws, cancellationToken);
            successDetails = successDetails with { Request = jws, Response = await response.Content.ReadAsStringAsync(cancellationToken) };
            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Request {RequestId}: Merchant {MerchantId} failed to notified for order {OrderId} and customer {CustomerId}", message.RequestId, message.MerchantId, orderId,
                    message.CustomerId);
                var failedDetails = successDetails with { Response = await response.Content.ReadAsStringAsync(cancellationToken) };
                var failedNotificationEvent = CreateNotificationEvent(message, EventTypes.MerchNotifyFailed, AppConstants.OrderEventDescriptions.MerchantNotificationFailed, failedDetails);

                await orderEventRepository.AddAsync(failedNotificationEvent, true, cancellationToken);
                failedResponseLogged = true;
                response.EnsureSuccessStatusCode();
            }

            var successNotificationEvent = CreateNotificationEvent(message, EventTypes.MerchNotifySuccessful, AppConstants.OrderEventDescriptions.MerchantNotifiedSuccessfully, successDetails);
            await orderEventRepository.AddAsync(successNotificationEvent, true, cancellationToken);
            logger.LogInformation("Request {RequestId}: Merchant {MerchantId} notified successfully for order {OrderId} and customer {CustomerId}", message.RequestId, message.MerchantId, orderId,
                message.CustomerId);
        }
        catch (HttpRequestException e)
        {
            if (failedResponseLogged) throw;
            var failToCommunicateEvent = CreateNotificationEvent(message, EventTypes.MerchNotifyFailed, AppConstants.OrderEventDescriptions.ConnectionToMerchantFailed,
                new EventDetails(Request: e.Message, Response: e.InnerException?.Message ?? "", Url: url));
            await orderEventRepository.AddAsync(failToCommunicateEvent, true, cancellationToken);
            logger.LogError("Request {RequestId}: Payment for order {OrderId} and customer {CustomerId} failed", message.RequestId, orderId, message.CustomerId);
            throw;
        }
        catch (Exception e)
        {
            logger.LogError("Request {RequestId}: Payment for order {OrderId} and customer {CustomerId} failed with exception {Exception}", message.RequestId, orderId, message.CustomerId, e.Message);
            throw;
        }
    }

    /// <summary>
    /// Processes order payment notification with manual retry flag set.
    /// This method bypasses the maximum retry limit check.
    /// </summary>
    public async Task ProcessOrderPaymentNotificationManualRetry(Guid orderId, OrderNotificationMessage message, CancellationToken cancellationToken, PerformContext? context = null)
    {
        // Set manual retry parameter
        context?.SetJobParameter("ManualRetry", true);

        // Call the main processing method
        await ProcessOrderPaymentNotification(orderId, message, cancellationToken, context);
    }

    /// <summary>
    /// Determines if the current job execution is a manual retry from Hangfire UI.
    /// Manual retries can be detected by checking if the job was triggered outside of the automatic retry mechanism.
    /// </summary>
    /// <param name="context">The Hangfire PerformContext</param>
    /// <returns>True if this is a manual retry, false otherwise</returns>
    private static bool IsManualRetry(PerformContext? context)
    {
        if (context == null) return false;

        // Check if the job has a "ManualRetry" parameter set (this would be set when manually retrying)
        var manualRetryParam = context.GetJobParameter<bool?>("ManualRetry");
        if (manualRetryParam.HasValue && manualRetryParam.Value)
        {
            return true;
        }

        // Alternative approach: Check if the job was created recently but has a high retry count
        // This indicates it might be a manual retry of a previously failed job
        var jobCreatedAt = context.BackgroundJob.CreatedAt;
        var retryCount = context.GetJobParameter<int>("RetryCount");
        var timeSinceCreation = DateTimeOffset.UtcNow - jobCreatedAt;

        // If the job was created recently (within last 5 minutes) but has retry count > 0,
        // it's likely a manual retry from UI
        if (timeSinceCreation.TotalMinutes < 5 && retryCount > 0)
        {
            return true;
        }

        // Check if this job was manually enqueued (no automatic retry delay)
        // Manual retries typically don't have the automatic retry delay pattern
        var automaticRetryAttempt = context.GetJobParameter<int?>("AutomaticRetryAttempt");
        return automaticRetryAttempt == null && retryCount > 0;
        // This suggests it's not an automatic retry, likely manual
    }

    private static OrderEvent CreateNotificationEvent(OrderNotificationMessage message, EventTypes eventType, string description, EventDetails eventDetails)
    {
        var successNotificationEvent = new OrderEvent
        {
            OrderId = OrderId.From(message.OrderId),
            EventDetails = JsonSerializer.Serialize(eventDetails),
            EventType = eventType,
            OrderStatus = message.OrderStatus,
            CreatedAt = DateTimeOffset.UtcNow,
            Id = OrderEventId.New(),
            IpAddress = message.IpAddress,
            RequestId = message.RequestId,
            Description = description
        };
        return successNotificationEvent;
    }
}

public record EventDetails(string Request, string Response, string Url);
