using System.Text.Json;
using Hangfire.Server;
using PaymentGateway.Integrations.Services.NotificationClient;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Payments.Services;

public class MerchantNotifier(
    ILogger<MerchantNotifier> logger,
    INotificationSender client,
    IOrderEventRepository orderEventRepository,
    IMerchantConfigRepository merchantConfigRepository,
    IJwsCreator jwsCreator) : IMerchantNotifier
{
    public async Task ProcessOrderPaymentNotification(Guid orderId, OrderNotificationMessage message, CancellationToken cancellationToken, PerformContext? context = null)
    {
        var config = await merchantConfigRepository.FindByMerchantId(message.MerchantId, cancellationToken);
        if (config is null) return;
        var maxRetryCount = config.MaxNotificationRetries;
        var url = config.PublicAPIUrl;
        var retryCount = context?.GetJobParameter<int>("RetryCount") ?? 0;
        var merchantName = config.Name;
        var maxRetryReached = retryCount == maxRetryCount;
        var successDetails = new EventDetails(Request: "", Response: "", Url: url);

        if (!maxRetryReached)
        {
            logger.LogInformation("Request {requestId}: Retry count {RetryCount} for for order {OrderId} and customer {CustomerId}", message.RequestId, retryCount, orderId, message.CustomerId);
        }
        else
        {
            logger.LogInformation("Request {requestId}: Max retry count {MaxRetryCount} reached for merchant {merchantId} Processing payment for order {OrderId} and customer {CustomerId}",
                message.RequestId, maxRetryCount, message.MerchantId, orderId, message.CustomerId);
            var orderEvent = CreateNotificationEvent(message, EventTypes.MerchNotifyFailed, AppConstants.OrderEventDescriptions.MerchantMaxNotificationTriesReached,
                successDetails with { Request = $"Maximum tries ({maxRetryCount}) reached!", });
            await orderEventRepository.AddAsync(orderEvent, true, cancellationToken);
            return;
        }

        var failedResponseLogged = false;
        try
        {
            var payload = new MerchantNotificationPayload(orderId, message.ExternalId, message.MerchantId, merchantName,
                [new StatusChange(message.OrderId, message.ExternalId, message.OrderStatus, DateTimeOffset.UtcNow),], retryCount);
            var jws = jwsCreator.CreateSignedJws(payload);
            var response = await client.SendMessageAsync(url, jws, cancellationToken);
            successDetails = successDetails with { Request = jws, Response = await response.Content.ReadAsStringAsync(cancellationToken) };
            if (!response.IsSuccessStatusCode)
            {
                logger.LogError("Request {RequestId}: Merchant {MerchantId} failed to notified for order {OrderId} and customer {CustomerId}", message.RequestId, message.MerchantId, orderId,
                    message.CustomerId);
                var failedDetails = successDetails with { Response = await response.Content.ReadAsStringAsync(cancellationToken) };
                var failedNotificationEvent = CreateNotificationEvent(message, EventTypes.MerchNotifyFailed, AppConstants.OrderEventDescriptions.MerchantNotificationFailed, failedDetails);

                await orderEventRepository.AddAsync(failedNotificationEvent, true, cancellationToken);
                failedResponseLogged = true;
                response.EnsureSuccessStatusCode();
            }

            var successNotificationEvent = CreateNotificationEvent(message, EventTypes.MerchNotifySuccessful, AppConstants.OrderEventDescriptions.MerchantNotifiedSuccessfully, successDetails);
            await orderEventRepository.AddAsync(successNotificationEvent, true, cancellationToken);
            logger.LogInformation("Request {RequestId}: Merchant {MerchantId} notified successfully for order {OrderId} and customer {CustomerId}", message.RequestId, message.MerchantId, orderId,
                message.CustomerId);
        }
        catch (HttpRequestException e)
        {
            if (failedResponseLogged) throw;
            var failToCommunicateEvent = CreateNotificationEvent(message, EventTypes.MerchNotifyFailed, AppConstants.OrderEventDescriptions.ConnectionToMerchantFailed,
                new EventDetails(Request: e.Message, Response: e.InnerException?.Message ?? "", Url: url));
            await orderEventRepository.AddAsync(failToCommunicateEvent, true, cancellationToken);
            logger.LogError("Request {RequestId}: Payment for order {OrderId} and customer {CustomerId} failed", message.RequestId, orderId, message.CustomerId);
            throw;
        }
        catch (Exception e)
        {
            logger.LogError("Request {RequestId}: Payment for order {OrderId} and customer {CustomerId} failed with exception {e}", message.RequestId, orderId, message.CustomerId, e);
            throw;
        }
    }

    private static OrderEvent CreateNotificationEvent(OrderNotificationMessage message, EventTypes eventType, string description, EventDetails eventDetails)
    {
        var successNotificationEvent = new OrderEvent
        {
            OrderId = OrderId.From(message.OrderId),
            EventDetails = JsonSerializer.Serialize(eventDetails),
            EventType = eventType,
            OrderStatus = message.OrderStatus,
            CreatedAt = DateTimeOffset.UtcNow,
            Id = OrderEventId.New(),
            IpAddress = message.IpAddress,
            RequestId = message.RequestId,
            Description = description
        };
        return successNotificationEvent;
    }
}

public record EventDetails(string Request, string Response, string Url);
