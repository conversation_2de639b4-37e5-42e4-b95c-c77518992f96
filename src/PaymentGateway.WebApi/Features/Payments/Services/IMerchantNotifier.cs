using Hangfire;
using Hangfire.Server;

namespace PaymentGateway.WebApi.Features.Payments.Services;

public interface IMerchantNotifier
{
    [JobDisplayName("Merchant notification process for Order with Id {0}")]
    Task ProcessOrderPaymentNotification(Guid orderId,OrderNotificationMessage message, CancellationToken cancellationToken, PerformContext? context = null);

    [JobDisplayName("Manual retry: Merchant notification process for Order with Id {0}")]
    Task ProcessOrderPaymentNotificationManualRetry(Guid orderId, OrderNotificationMessage message, CancellationToken cancellationToken, PerformContext? context = null);
}
