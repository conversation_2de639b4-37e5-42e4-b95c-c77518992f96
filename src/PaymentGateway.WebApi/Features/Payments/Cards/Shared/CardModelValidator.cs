// using FastEndpoints;
// using FluentValidation;
//
// namespace PaymentGateway.WebApi.Features.Payments.Cards.Shared;
//
// public sealed class CardModelValidator : Validator<CardModel>
// {
//     public CardModelValidator()
//     {
//         RuleFor(x => x.CardNumber)
//             .Matches(@"^\d{16}$")
//             .WithErrorCode(CardErrors.CardNumberBe16Digits.Code)
//             .WithMessage(CardErrors.CardNumberBe16Digits.Message);
//         RuleFor(x => x.YearOfExpiry)
//             .Matches(@"^\d{4}$")
//             .WithErrorCode(CardErrors.YearOfExpiryShouldBe4Digits.Code)
//             .WithMessage(CardErrors.YearOfExpiryShouldBe4Digits.Message)
//             .Must(x =>
//             {
//                 if (!int.TryParse(x, out var year)) return false;
//                 var currentYear = DateTime.UtcNow.Year;
//                 return year >= currentYear && year <= currentYear + 50;
//             })
//             .WithMessage(CardErrors.YearOfExpiryShouldBeBetweenCurrentYearAndNext50.Message)
//             .WithErrorCode(CardErrors.YearOfExpiryShouldBeBetweenCurrentYearAndNext50.Code);
//         RuleFor(x => x.MonthOfExpiry)
//             .Matches(@"^\d{2}$")
//             .WithErrorCode(CardErrors.MonthOfExpiryShouldBe2Digits.Code)
//             .WithMessage(CardErrors.MonthOfExpiryShouldBe2Digits.Message)
//             .Must(x =>
//             {
//                 if (!int.TryParse(x, out var month)) return false;
//                 return month is >= 1 and <= 12;
//             })
//             .WithMessage(CardErrors.MonthOfExpiryShouldBeBetween1And12.Message)
//             .WithErrorCode(CardErrors.MonthOfExpiryShouldBeBetween1And12.Code);
//         RuleFor(x => x.CardHolderName)
//             .Matches("^$|^[a-zA-Z][a-zA-Z0-9$()\\-. ]*$")
//             .WithMessage(CardErrors.CardHolderNameShouldValidName.Message)
//             .WithErrorCode(CardErrors.CardHolderNameShouldValidName.Code)
//             .Must(x => x.Length < 27)
//             .WithMessage(CardErrors.CardHolderNameShouldBeLessThan27Characters.Message)
//             .WithErrorCode(CardErrors.CardHolderNameShouldBeLessThan27Characters.Code)
//             .When(x => x.CardHolderName != null);
//     }
// }
