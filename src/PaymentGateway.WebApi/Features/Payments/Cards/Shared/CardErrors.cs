// using PaymentGateway.WebApi.Common;
//
// namespace PaymentGateway.WebApi.Features.Payments.Cards.Shared;

// public abstract class CardErrors
// {
//     public static Error CardNumberBe16Digits => Error.Create(ErrorNumbers.CardErrors.CardNumberBe16Digits,
//         nameof(CardNumberBe16Digits), "Card number should be 16 digits");
//     public static Error YearOfExpiryShouldBeBetweenCurrentYearAndNext50 => Error.Create(ErrorNumbers.CardErrors.YearOfExpiryShouldBeBetweenCurrentYearAndNext50,nameof(YearOfExpiryShouldBeBetweenCurrentYearAndNext50), 
//         "Year of expiry should be between current year and next 50");
//     public static Error MonthOfExpiryShouldBeBetween1And12 => Error.Create( ErrorNumbers.CardErrors.MonthOfExpiryShouldBeBetween1And12,
//         nameof(MonthOfExpiryShouldBeBetween1And12), 
//         "Month of expiry should be between 1 and 12");
//     public static Error CardHolderNameShouldValidName => Error.Create(ErrorNumbers.CardErrors.CardHolderNameShouldValidName,
//         nameof(CardHolderNameShouldValidName), "Card holder name should be valid name");
//     public static Error YearOfExpiryShouldBe4Digits => Error.Create(ErrorNumbers.CardErrors.YearOfExpiryShouldBe4Digits,
//         nameof(YearOfExpiryShouldBe4Digits), "Year of expiry should be 4 digits");
//     public static Error MonthOfExpiryShouldBe2Digits => Error.Create( ErrorNumbers.CardErrors.MonthOfExpiryShouldBe2Digits,
//         nameof(MonthOfExpiryShouldBe2Digits), "Month of expiry should be 2 digits");
//     public static Error CardHolderNameShouldBeLessThan27Characters => Error.Create( ErrorNumbers.CardErrors.CardHolderNameShouldBeLessThan27Characters,
//         nameof(CardHolderNameShouldBeLessThan27Characters),
//         "Card holder name should be 26 characters");
// }
