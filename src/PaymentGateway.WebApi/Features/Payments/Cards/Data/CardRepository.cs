// using PaymentGateway.WebApi.Data;
// using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Payments.OrderPay;
//
// namespace PaymentGateway.WebApi.Features.Payments.Cards.Data;
//
// public sealed class CardRepository(PaymentDbContext context, AutoMapper.IMapper mapper) : Repository<Card, CardId>(context), ICardRepository
// {
//     public async Task UpsertCard(OrderPayRequest request, string customerId, CancellationToken cancellationToken)
//     {
//         var card = await FindAsync(x => x.CustomerId == CustomerId.From(customerId) && x.CardNumber == request.Card.CardNumber, cancellationToken);
//
//         if (card is not null)
//         {
//             var holderNameChanged = UpdateCardHolderNameIfDoesNotMatchTheRequest(request, card);
//             var yearChanged = YearNeedToUpdate(request, card);
//             var monthChanged = MonthNeedToUpdate(request, card);
//             if (holderNameChanged || yearChanged || monthChanged)
//             {
//                 card.UpdatedAt = DateTimeOffset.UtcNow;
//                 await UpdateAsync(card, true, cancellationToken);
//             }
//         }
//         else
//         {
//             var newCard = mapper.Map<Card>(request.Card);
//             newCard.Id = CardId.New();
//             newCard.CustomerId = CustomerId.From(customerId);
//             await AddAsync(newCard, true, cancellationToken);
//         }
//     }
//
//     private static bool YearNeedToUpdate(OrderPayRequest request, Card card)
//     {
//         if (card.YearOfExpiry == request.Card.YearOfExpiry) return false;
//         card.YearOfExpiry = request.Card.YearOfExpiry;
//         return true;
//     }
//
//     private static bool MonthNeedToUpdate(OrderPayRequest request, Card card)
//     {
//         if (card.MonthOfExpiry == request.Card.MonthOfExpiry) return false;
//         card.MonthOfExpiry = request.Card.MonthOfExpiry;
//         return true;
//     }
//
//     private static bool UpdateCardHolderNameIfDoesNotMatchTheRequest(OrderPayRequest request, Card card)
//     {
//         if (card.CardHolderName == request.Card.CardHolderName) return false;
//         card.CardHolderName = request.Card.CardHolderName;
//         return true;
//     }
// }
