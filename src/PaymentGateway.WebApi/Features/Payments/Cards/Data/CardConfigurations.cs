using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PaymentGateway.WebApi.Features.Orders.Data;

// namespace PaymentGateway.WebApi.Features.Payments.Cards.Data;

// public sealed class CardConfigurations : IEntityTypeConfiguration<Card>
// {
//     public void Configure(EntityTypeBuilder<Card> builder)
//     {
//         builder.HasKey(x => x.Id);
//         builder.HasIndex(x => new {x.CustomerId, x.CardNumber})
//             .IsUnique();
//         builder.Property(x => x.CardNumber)
//             .IsRequired();
//         builder.Property(x => x.YearOfExpiry)
//             .IsRequired();
//         builder.Property(x => x.MonthOfExpiry)
//             .IsRequired();
//         builder.Property(x => x.CardHolderName);
//
//         builder.Property(x => x.Id)
//             .HasMaxLength(26)
//             .HasConversion(x => x.ToString(), x => CardId.From(Ulid.Parse(x)));
//
//         builder.Property(x => x.CustomerId).HasMaxLength(255).HasConversion(v => v.Value, v => CustomerId.From(v));
//
//     }
// }
