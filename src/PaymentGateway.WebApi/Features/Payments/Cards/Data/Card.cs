// using PaymentGateway.WebApi.Common;
// using PaymentGateway.WebApi.Features.Orders.Data;
// using Vogen;
//
// namespace PaymentGateway.WebApi.Features.Payments.Cards.Data;

// public sealed class Card : EntityBase<CardId>
// {
//     public required string CardNumber { get; init; }
//
//     public required CustomerId CustomerId { get; set; }
//     public required string YearOfExpiry { get; set; }
//     public required string MonthOfExpiry { get; set; }
//     public string? CardHolderName { get; set; }
//     public bool IsActive { get; init; }
//     public DateTimeOffset? UpdatedAt { get; set; }
// }
//
// [ValueObject<Ulid>]
// public partial struct CardId
// {
//     public static CardId New() =>From(Ulid.NewUlid());
// }