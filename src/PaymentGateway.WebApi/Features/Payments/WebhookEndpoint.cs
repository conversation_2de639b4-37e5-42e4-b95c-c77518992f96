using FastEndpoints;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Payments.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace PaymentGateway.WebApi.Features.Payments;

public class WebhookEndpoint(
    ILogger<WebhookEndpoint> logger,
    IClickhouseTaskBuilder clickhouseTaskBuilder,
    IHangfireJobQueueService jobQueueService,
    IOrderRepository orderRepository,
    IOrderEventRepository orderEventRepository,
    IOrderUpserter orderUpserter,
    IMerchantNotifier merchantNotifier) : Endpoint<WebhookRequest, IResult>
{
    public override void Configure()
    {
        Post(AppRoute.Tabadul.Webhook);
        AllowAnonymous();
    }

    public override async Task<IResult> ExecuteAsync(WebhookRequest req, CancellationToken ct)
    {
        logger.LogInformation("Received Webhook Request {Mdorder} with status {Status}", req.Mdorder, req.Status);
        var orderId = OrderId.From(req.Mdorder);
        logger.LogInformation("Order id {OrderId} parsed successfully", orderId);
        var order = await orderRepository.FindAsync(x => x.Id == orderId, ct);
        if (order is null)
        {
            logger.LogError("Webhook: Order {OrderId} not found", orderId);
            return Results.NotFound();
        }

        if (req is
            {
                Status: "1",
                PaymentState: "payment_deposited",
            })
            await OrderPaidSuccessfulProcess(order, ct);
        else
            await OrderPaidFailedProcess(order, ct);
        return Results.Ok();
    }

    private async Task OrderPaidSuccessfulProcess(Order order, CancellationToken ct)
    {
        const OrderStatuses successOrderStatus = OrderStatuses.Successful;
        const EventTypes successEventType = EventTypes.OrderPaymentSuccessful;
        logger.LogInformation("{Object}: Order {OrderId} payment successful using Webhook", nameof(WebhookEndpoint), order.Id);
        await AddOrderEventAsync(order.Id, successEventType, successOrderStatus, AppConstants.OrderEventDescriptions.OrderConfirmSuccessful, null, null, ct);
        NotifyMerchant(order, successOrderStatus, null, null, ct);
        // send to clickhouse
        
    }

    private async Task OrderPaidFailedProcess(Order order, CancellationToken ct)
    {
        const OrderStatuses failedOrderStatus = OrderStatuses.FailedNoRetry;
        const EventTypes eventType = EventTypes.OrderPaymentFailed;
        logger.LogInformation("{Object}: Order {OrderId} payment failed using Webhook", nameof(WebhookEndpoint), order.Id);
        await AddOrderEventAsync(order.Id, eventType, failedOrderStatus, AppConstants.OrderEventDescriptions.OrderPaymentFailed, null, null, ct);
        NotifyMerchant(order, failedOrderStatus, null, null, ct);
    }

    private void NotifyMerchant(Order order, OrderStatuses successOrderStatus, string? requestId, string? ipAddress, CancellationToken ct)
    {
        var orderNotificationMessage = CreateOrderNotificationMessage(order, requestId, ipAddress, successOrderStatus);
        var jobId = jobQueueService.Enqueue<IMerchantNotifier>(x => x.ProcessOrderPaymentNotification(order.Id.Value, orderNotificationMessage, ct, null));
        logger.LogInformation("{Object}: RequestId {RequestId} for Order {OrderId} started job {JobId} to notify merchant {MerchantId} the OrderStatus {OrderStatus} at {CreatedAt}",
            nameof(WebhookEndpoint), requestId, order.Id, jobId, order.MerchantId, successOrderStatus, DateTimeOffset.UtcNow);
    }

    private static OrderNotificationMessage CreateOrderNotificationMessage(Order order, string? requestId, string? ipAddress, OrderStatuses successOrderStatus)
    {
        var orderNotificationMessage = new OrderNotificationMessage(order.Id.Value, order.ExternalId, order.CustomerId, requestId, ipAddress, order.MerchantId, successOrderStatus);
        return orderNotificationMessage;
    }

    private async Task AddOrderEventAsync(OrderId orderId, EventTypes eventType, OrderStatuses orderStatus, string description, string? requestId, string? ipAddress, CancellationToken ct)
    {
        var orderEvent = new OrderEvent
        {
            OrderId = orderId,
            EventType = eventType,
            OrderStatus = orderStatus,
            Description = description,
            EventDetails = "null",
            CreatedAt = DateTimeOffset.UtcNow,
            Id = OrderEventId.New(),
            IpAddress = ipAddress,
            RequestId = requestId,
        };

        await orderEventRepository.AddAsync(orderEvent, true, ct);
    }
}
