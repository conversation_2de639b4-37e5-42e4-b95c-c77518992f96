// using FastEndpoints;
// using PaymentGateway.Integrations.Services.Tabadul;
// using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;
// using PaymentGateway.WebApi.Common;
// using PaymentGateway.WebApi.Common.Services;
// using PaymentGateway.WebApi.Common.Services.Hangfire;
// using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Orders.Services;
// using PaymentGateway.WebApi.Features.Orders.Shared;
// using PaymentGateway.WebApi.Features.Payments.Cards.Data;
// using PaymentGateway.WebApi.Features.Payments.Services;
// using IMapper = AutoMapper.IMapper;
// using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;
//
// namespace PaymentGateway.WebApi.Features.Payments.OrderPay;
//
// public sealed class OrderPayEndpoint(
//     IClickhouseTaskBuilder clickhouseTaskBuilder,
//     IOrderRepository orderRepository,
//     ILogger<OrderPayEndpoint> logger,
//     IOrderEventRepository orderEventRepository,
//     ITabadulOrderService tabadulOrderService,
//     ICardRepository cardRepository,
//     IOrderUpserter orderUpserter,
//     IHangfireJobQueueService jobQueueService,
//     IMapper mapper,
//     IIpResolver ipResolver,
//     IRequestIdResolver requestIdResolver) : Endpoint<OrderPayRequest, OrderPayResponse>
// {
//     public override void Configure()
//     {
//         Post(AppRoute.Orders.Pay);
//         AuthSchemes(AppConstants.SecuritySchemes.ApiKeySchemeName);
//     }
//
//     private static Result<Guid> TryGetOrderId(OrderPayRequest request) => request.Id == Guid.Empty ? Result<Guid>.Failure(OrderErrors.OrderIdIsNotValid) : Result<Guid>.Success(request.Id);
//
//     public override async Task HandleAsync(OrderPayRequest request, CancellationToken cancellationToken)
//     {
//         var requestId = requestIdResolver.GetRequestId();
//         var ipAddress = ipResolver.GetIp();
//         var tryGetOrderIdResult = TryGetOrderId(request);
//         if (tryGetOrderIdResult.IsFailure)
//         {
//             await Send.ResultAsync(tryGetOrderIdResult.Error.Problem());
//             return;
//         }
//
//         var order = await orderRepository.FindAsync(x => x.Id == OrderId.From(request.Id), cancellationToken);
//         if (order is null)
//         {
//             await Send.NotFoundAsync(cancellationToken);
//             return;
//         }
//
//         var orderIsClosed = await orderEventRepository.OrderIsClosedAsync(order.Id, cancellationToken);
//         if (orderIsClosed)
//         {
//             await Send.ResultAsync(OrderErrors.OrderIsAlreadyPaid.Problem());
//             return;
//         }
//
//         var payToTabadulResult = await ProcessPaymentAsync(request, cancellationToken, order, requestId, ipAddress);
//         if (payToTabadulResult.IsFailure)
//         {
//             await Send.ResultAsync(payToTabadulResult.Error.Problem());
//             return;
//         }
//
//         clickhouseTaskBuilder.SendToClickhouse(request, ipAddress, requestId, order, cancellationToken);
//         await ExecuteAfterSuccessfulPaymentProcedure(request, order, payToTabadulResult, requestId, ipAddress, cancellationToken);
//         var response = BuildEndpointResponse(payToTabadulResult);
//
//         await SendOkAsync(response, cancellationToken);
//     }
//
//     private OrderPayResponse BuildEndpointResponse(Result<TabadulOrderPaymentResponse> payToTabadulResult)
//     {
//         var response = mapper.Map<OrderPayResponse>(payToTabadulResult.Data);
//
//         response.OrderStatus = payToTabadulResult.Data.OrderStatusCode switch
//         {
//             TabadulOrderStatuses.DepositedSuccessfully => OrderStatuses.Successful,
//             _ => OrderStatuses.FailedNoRetry,
//         };
//         return response;
//     }
//
//     private async Task ExecuteAfterSuccessfulPaymentProcedure(OrderPayRequest request, Order order, Result<TabadulOrderPaymentResponse> payToTabadulResult, string requestId, string ipAddress,
//         CancellationToken cancellationToken)
//     {
//         await orderUpserter.AddSuccessPaymentEventAsync(order.Id.Value, payToTabadulResult.Data, requestId, ipAddress, true, cancellationToken);
//         await cardRepository.UpsertCard(request, order.CustomerId.Value, cancellationToken);
//         SendOrderNotificationMessage(order, requestId, ipAddress, OrderStatuses.Successful, cancellationToken);
//         logger.LogInformation("{Object}: Order {OrderId} paid successfully with RequestId {RequestId} ", nameof(OrderPayEndpoint), order.Id, requestId);
//     }
//
//     private async Task<Result<TabadulOrderPaymentResponse>> ProcessPaymentAsync(OrderPayRequest request, CancellationToken cancellationToken, Order order, string requestId, string ipAddress)
//     {
//         var payToTabadulResult = await tabadulOrderService.PayToTabadulAsync(request, cancellationToken);
//         if (payToTabadulResult.IsSuccess) return payToTabadulResult;
//         var payToTabadulError = payToTabadulResult.Error;
//         await orderUpserter.AddFailurePaymentEventAsync(order.Id.Value, payToTabadulError, requestId, ipAddress, true, cancellationToken);
//         SendOrderNotificationMessage(order, requestId, ipAddress, OrderStatuses.FailedNoRetry, cancellationToken);
//         return payToTabadulResult;
//     }
//     
//     private void SendOrderNotificationMessage(Order order, string requestId, string ipAddress, OrderStatuses orderStatus, CancellationToken cancellationToken)
//     {
//         var notificationMessage = new OrderNotificationMessage(order.Id.Value, order.ExternalId, order.CustomerId, requestId, ipAddress, order.MerchantId, orderStatus);
//         jobQueueService.Enqueue<IMerchantNotifier>(job => job.ProcessOrderPaymentNotification(order.Id.Value, notificationMessage, cancellationToken, null));
//     }
// }
