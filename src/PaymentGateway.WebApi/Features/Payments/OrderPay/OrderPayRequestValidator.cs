// using System.Text.RegularExpressions;
// using FastEndpoints;
// using FluentValidation;
// using PaymentGateway.Integrations.Extensions;
// using PaymentGateway.WebApi.Features.Orders.Shared;
// // using PaymentGateway.WebApi.Features.Payments.Cards.Shared;
//
// namespace PaymentGateway.WebApi.Features.Payments.OrderPay;
//
// public sealed class OrderPayRequestValidator : Validator<OrderPayRequest>
// {
//     public OrderPayRequestValidator()
//     {
//         RuleFor(x => x.Id)
//             .NotEmpty();
//         // RuleFor(x => x.Card)
//         //     .SetValidator(new CardModelValidator());
//         RuleFor(x => x.Cvv)
//             .Must(x => x.IsNotNullOrWhiteSpace() && Regex.IsMatch(x, @"^\d{3}$"))
//             .WithErrorCode(OrderErrors.CvvShouldBe3Digits.Code)
//             .WithMessage(OrderErrors.CvvShouldBe3Digits.Message);
//     }
// }
