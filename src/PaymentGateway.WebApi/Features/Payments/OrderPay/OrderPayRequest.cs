using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Payments.Cards.Shared;

namespace PaymentGateway.WebApi.Features.Payments.OrderPay;

//todo: add language in header
public record OrderPayRequest(Guid Id
    // , CardModel Card
    , string Cvv);

public class OrderPayResponse
{
    public required OrderStatuses OrderStatus{ get; set; }
    public required string TransactionDataElements { get; init; }
    public required string Info { get; init; }
}
