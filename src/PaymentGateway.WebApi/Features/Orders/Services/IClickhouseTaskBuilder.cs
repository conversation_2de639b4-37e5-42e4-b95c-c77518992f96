using System.Text.Json;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public interface IClickhouseTaskBuilder
{
    void SendToClickhouse<T>(T req, string ipAddress, string requestId, Order order, CancellationToken ct);
}

public class ClickhouseTaskBuilder(IHangfireJobQueueService jobQueueService, ILogger<IClickhouseTaskBuilder> logger) : IClickhouseTaskBuilder
{
    public void SendToClickhouse<T>(T req, string ipAddress, string requestId, Order order, CancellationToken ct)
    {
        var orderEvent = new OrderEventModel(EventTypes.OrderCreated, OrderStatuses.Initiated, JsonSerializer.Serialize(req), AppConstants.OrderEventDescriptions.OrderCreated, ipAddress, requestId);
        var jobId = jobQueueService.Enqueue<IClickhouseInserter>(job => job.InsertAsync(order.Id.Value, order, orderEvent, ct));
        logger.LogInformation("{Object}: Order {OrderId} with JobId {JobId} and RequestId {RequestId} will send to Clickhouse inserter service", nameof(T), order.Id, jobId, requestId);
    }
}
