using System.Text.Json;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public class OrderUpserter(IOrderEventRepository orderEventRepository, IOrderRepository orderRepository) : IOrderUpserter
{
    public async Task CreateOrderAsync(Order order, string requestId, string ipAddress, bool saveChanges, string? description = null, CancellationToken ct = default)
    {
        await orderRepository.AddAsync(order, saveChanges, ct);
        var orderEvent = new OrderEvent
        {
            Id = OrderEventId.From(Ulid.NewUlid()),
            OrderId = order.Id,
            EventDetails = JsonSerializer.Serialize(order),
            EventType = EventTypes.OrderCreated,
            CreatedAt = DateTimeOffset.UtcNow,
            Description = description,
            OrderStatus = OrderStatuses.Initiated,
            IpAddress = requestId,
            RequestId = ipAddress,
        };

        await orderEventRepository.AddAsync(orderEvent, saveChanges, ct);
    }

    public async Task AddSuccessPaymentEventAsync<T>(Guid orderId, T response, string requestId, string ipAddress, bool saveChanges, CancellationToken cancellationToken)
    {
        var successOrderEvent = new OrderEvent
        {
            Id = OrderEventId.New(),
            OrderId = OrderId.From(orderId),
            EventDetails = JsonSerializer.Serialize(response),
            EventType = EventTypes.OrderPaymentSuccessful,
            OrderStatus = OrderStatuses.Successful,
            CreatedAt = DateTimeOffset.UtcNow,
            Description = AppConstants.OrderEventDescriptions.OrderPaymentSuccessful,
            IpAddress = ipAddress,
            RequestId = requestId,
        };
        await orderEventRepository.AddAsync(successOrderEvent, saveChanges, cancellationToken);
    }

    public async Task AddFailurePaymentEventAsync(Guid orderId, Error error, string requestId, string ipAddress, bool saveChanges, CancellationToken cancellationToken)
    {
        var failureOrderEvent = new OrderEvent
        {
            Id = OrderEventId.New(),
            OrderId = OrderId.From(orderId),
            EventDetails = JsonSerializer.Serialize(error),
            EventType = EventTypes.OrderPaymentFailed,
            OrderStatus = OrderStatuses.FailedNoRetry,
            CreatedAt = DateTimeOffset.UtcNow,
            Description = AppConstants.OrderEventDescriptions.OrderPaymentFailed,
            IpAddress = ipAddress,
            RequestId = requestId,
        };
        await orderEventRepository.AddAsync(failureOrderEvent, saveChanges, cancellationToken);
    }
}
