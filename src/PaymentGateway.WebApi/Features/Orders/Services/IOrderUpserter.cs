using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Orders.Data;
namespace PaymentGateway.WebApi.Features.Orders.Services;

public interface IOrderUpserter
{
    Task CreateOrderAsync(Order order,string requestId, string ipAddress, bool saveChanges, string? description = null, CancellationToken ct = default);
    Task AddSuccessPaymentEventAsync<T>(Guid orderId, T response,string requestId, string ipAddress, bool saveChanges, CancellationToken cancellationToken);
    Task AddFailurePaymentEventAsync(Guid orderId, Error request,string requestId, string ipAddress, bool saveChanges, CancellationToken cancellationToken);
}