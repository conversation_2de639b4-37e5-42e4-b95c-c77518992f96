using Microsoft.EntityFrameworkCore;
using PaymentGateway.Integrations.Services;
using PaymentGateway.Integrations.Services.Tabadul.Auth;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Merchants.Data;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public class MerchantCredentialProvider(PaymentDbContext context, IClaimsExtractor claimsExtractor, ICachingService cachingService) : IMerchantCredentialProvider
{
    public async Task<MerchantCredentialModel> GetMerchantCredentialsAsync(string merchantId, CancellationToken cancellationToken)
    {
       

        if (!MerchantId.TryFrom(merchantId, out var mId)) throw new Exception("Invalid MerchantId in the claims");

        var cacheKey = $"{AppConstants.CachePrefixes.MerchantCredentialPrefix}{mId}";
        var merchantCredentialsFromCache = await cachingService.GetAsync<MerchantCredentialModel>(cacheKey);
        if (merchantCredentialsFromCache != null) return merchantCredentialsFromCache;
        var merchantCredentials = await context.MerchantLogins
            .Where(x => x.MerchantId == mId)
            .Where(x => x.MerchantTabadulLogin != null)
            .Select(x => x.MerchantTabadulLogin)
            .Select(x => new MerchantCredentialModel(x!.TabadulUsername, x.TabadulPassword))
            .FirstOrDefaultAsync(cancellationToken);
        if (merchantCredentials == null) throw new Exception("Merchant tabadul login credentials not found");

        await cachingService.SetAsync(cacheKey, merchantCredentials);
        return merchantCredentials;
    }
}
