using PaymentGateway.Integrations.Services;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.Integrations.Services.Tabadul.OrderGetStatusModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderRegisterModels;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Shared;
using PaymentGateway.WebApi.Features.Payments.OrderPay;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public class TabadulOrderService(
    IClient client,
    ILogger<TabadulOrderService> logger,
    IRequestIdResolver requestIdResolver,
    OrderConfiguration orderConfiguration,
    IMerchantCredentialProvider merchantCredentialProvider,
    IClaimsExtractor claimsExtractor) : ITabadulOrderService
{
    public async Task<Result<OrderGetStatusResponse>> FindOrderStatus(Guid orderId, MerchantId merchantId, string requestId, CancellationToken cancellationToken = default)
    {
        var (userName, password) = await merchantCredentialProvider.GetMerchantCredentialsAsync(merchantId.Value, cancellationToken);
        var orderStatusRequest = new OrderGetStatusRequest(orderId)
        {
            UserName = userName, Password = password,
        };
        var result = await client.GetStatus(orderStatusRequest, cancellationToken);
        if (result.OrderStatus is TabadulOrderStatuses.DepositedSuccessfully or TabadulOrderStatuses.OrderRegistered) return Result<OrderGetStatusResponse>.Success(result);

        logger.LogError("Request id: {requestId} Failed to pay to Tabadul for order {OrderId}. Error code: {ErrorCode}, Error message: {ErrorMessage}", requestId, orderId, result.ErrorCode,
            result.ErrorMessage);

        var error = TabadulErrorParser.Parse((int)result.ErrorCode, result.ErrorMessage, null);
        return Result<OrderGetStatusResponse>.Failure(error);
    }

    // public async Task<Result<TabadulOrderPaymentResponse>> PayToTabadulAsync(OrderPayRequest request, CancellationToken cancellationToken)
    // {
    //     var merchantId = claimsExtractor.GetMerchantId();
    //     var (userName, password) = await merchantCredentialProvider.GetMerchantCredentialsAsync(merchantId.Value, cancellationToken);
    //
    //     var orderPaymentRequest = new TabadulOrderPaymentRequest(request.Id.ToString(), request.Card.CardNumber, request.Cvv, request.Card.YearOfExpiry, request.Card.MonthOfExpiry)
    //     {
    //         UserName = userName, Password = password,
    //     };
    //     logger.LogInformation("Paying to Tabadul for order {OrderId}", request.Id);
    //     var orderPaymentResponse = await client.Pay(orderPaymentRequest, cancellationToken);
    //     if (orderPaymentResponse.OrderStatusCode == TabadulOrderStatuses.DepositedSuccessfully) return Result<TabadulOrderPaymentResponse>.Success(orderPaymentResponse);
    //
    //     logger.LogError("Request id: {requestId} Failed to pay to Tabadul for order {OrderId}. Error code: {ErrorCode}, Error message: {ErrorMessage}", request.Id, request.Id,
    //         orderPaymentResponse.ErrorCode, orderPaymentResponse.ErrorMessage);
    //
    //     var error = TabadulErrorParser.Parse(orderPaymentResponse.ErrorCode, orderPaymentResponse.ErrorMessage, orderPaymentResponse.Info);
    //     return Result<TabadulOrderPaymentResponse>.Failure(error);
    // }

    public async Task<Result<TabadulOrderRegisterResponse>> RegisterOrderAsync(Order order, string? returnUrl, CancellationToken cancellationToken = default)
    {
        var requestId = requestIdResolver.GetRequestId();
        logger.LogInformation("Register order with Tabadul service, requestId: {requestId}", requestId);

        var orderRegistrationRequest = await CreateOrderRegistrationRequest(order, returnUrl, cancellationToken);
        var response = await client.SendAsync<TabadulOrderRegisterRequest, TabadulOrderRegisterResponse>(AppConstants.Tabdul.OrderRegister, orderRegistrationRequest, cancellationToken);
        if (response.ErrorCode > 0)
        {
            logger.LogError("Failed to register order with Tabadul service, requestId: {requestId}, error: {error}", requestId, response.ErrorMessage);
            var error = TabadulErrorParser.Parse(response.ErrorCode, response.ErrorMessage, response.Info);
            return Result<TabadulOrderRegisterResponse>.Failure(error);
        }

        logger.LogInformation("Order registered with Tabadul service, requestId: {requestId}", requestId);
        return Result<TabadulOrderRegisterResponse>.Success(response);
    }

    private async Task<TabadulOrderRegisterRequest> CreateOrderRegistrationRequest(Order order, string? returnUrl, CancellationToken cancellationToken)
    {
        var (userName, password) = await merchantCredentialProvider.GetMerchantCredentialsAsync(order.MerchantId.Value, cancellationToken);
        var orderRegistrationRequest = CreateTabadulOrderRegisterRequest(order, returnUrl, userName, password);
        return orderRegistrationRequest;
    }

    private TabadulOrderRegisterRequest CreateTabadulOrderRegisterRequest(Order order, string? returnUrl, string userName, string password)
    {
        var orderRegistrationRequest = new TabadulOrderRegisterRequest(order.ExternalId.Value, order.CustomerId.ToString(), order.Amount, (int)CurrencyIso4217.IQD, null, returnUrl,
            AppConstants.OrderEventDescriptions.OrderCreated, order.Email!, null, orderConfiguration.DefaultLanguage)
        {
            UserName = userName, Password = password,
        };
        orderRegistrationRequest = SetAmountInMinorDenomination(orderRegistrationRequest);
        orderRegistrationRequest = RemoveDefaultClientIdToPreventBindingWithIt(orderRegistrationRequest);
        orderRegistrationRequest = CreateReturnUrls(order, returnUrl, orderRegistrationRequest);

        return orderRegistrationRequest;
    }
    private TabadulOrderRegisterRequest SetAmountInMinorDenomination(TabadulOrderRegisterRequest request)
    {
        var minorDenominationFactor = CurrencyFactors.CurrencyMinorDenominationFactor((CurrencyIso4217)request.Currency);
        var newAmount = request.Amount * minorDenominationFactor;
        logger.LogInformation("Amount converted from {amount} to {newAmount} due to currency factor of {minorDenominationFactor} before sending to Tabadul", request.Amount, newAmount, minorDenominationFactor);
        request = request with { Amount = newAmount, };
        return request;
    }
    private static TabadulOrderRegisterRequest CreateReturnUrls(Order order, string? returnUrl, TabadulOrderRegisterRequest orderRegistrationRequest)
    {
        if (returnUrl is not null)
            orderRegistrationRequest = orderRegistrationRequest with
            {
                FailUrl = $"{returnUrl}/statusRedirectUrl?Id={order.ExternalId}&status=1&statusToken={order.SecureFailureToken}",
                ReturnUrl = $"{returnUrl}/statusRedirectUrl?Id={order.ExternalId}&status=0&statusToken={order.SecureSuccessToken}",
            };
        return orderRegistrationRequest;
    }

    private static TabadulOrderRegisterRequest RemoveDefaultClientIdToPreventBindingWithIt(TabadulOrderRegisterRequest orderRegistrationRequest)
    {
        if (orderRegistrationRequest.ClientId == AppConstants.DefaultCustomerId) orderRegistrationRequest = orderRegistrationRequest with { ClientId = null, };
        return orderRegistrationRequest;
    }
}

public class CurrencyFactors
{
    private const int FilsInDinar = 1_000;
    private const int CentsInDollar = 100;

    public static int CurrencyMinorDenominationFactor(CurrencyIso4217 currency) => currency switch
    {
        CurrencyIso4217.IQD => FilsInDinar,
        CurrencyIso4217.USD => CentsInDollar,
        _ => 1
    };
}
