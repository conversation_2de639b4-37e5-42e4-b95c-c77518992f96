using Hangfire;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public record OrderClickhouseModel
{
    public required string Id { get; init; }
    public required string MerchantId { get; init; }
    public required string ExternalId { get; init; }
    public required string CustomerId { get; init; }
    public required long Amount { get; init; }
    public required string Currency { get; init; }
    public required string RequestId { get; init; }
    public required string? IpAddress { get; init; }
    public required string SecureSuccessToken { get; init; }
    public required string SecureFailureToken { get; init; }
    public required string JobId { get; init; }
    public required int ExpiresInSeconds { get; init; }
    public required string? Phone { get; init; }
    public required string? Email { get; init; }
    public required string EventDetails { get; init; }
    public required string EventType { get; init; }
    public required string OrderStatus { get; init; }
    public required string Description { get; init; }
    public required DateTimeOffset CreatedAt { get; init; }
}

public interface IClickhouseInserter
{
    [JobDisplayName("Sending Order to clickhouse with Id {0}")]

    Task InsertAsync(Guid orderId, Order order, OrderEventModel orderEvent, CancellationToken cancellationToken);
}

public sealed record OrderEventModel(EventTypes EventType, OrderStatuses OrderStatus, string EventDetails, string Description, string IpAddress, string RequestId);

public sealed class ClickhouseInserter(IClickhouseContext<OrderClickhouseModel> context, ILogger<ClickhouseInserter> logger, OrderConfiguration orderConfiguration) : IClickhouseInserter
{
    public async Task InsertAsync(Guid orderId, Order order, OrderEventModel orderEvent, CancellationToken cancellationToken)
    {
        logger.LogInformation("ClickhouseInserter: Inserting Order {OrderId} to clickhouse with Job {JobId} and request id {RequestId} sending to clickhouse at {CreatedAt}", orderId, order.JobId,
            orderEvent.RequestId, order.CreatedAt);
        var query = $"""
                             INSERT INTO {orderConfiguration.TableName}
                             (
                                 Id,
                                 MerchantId,
                                 ExternalId,
                                 CustomerId,
                                 Amount,
                                 Currency,
                                 RequestId,
                                 IpAddress,
                                 SecureSuccessToken,
                                 SecureFailureToken,
                                 JobId,
                                 ExpiresInSeconds,
                                 Phone,
                                 Email,
                                 EventDetails,
                                 EventType,
                                 OrderStatus,
                                 Description,
                                 CreatedAt
                             )
                             VALUES
                             (
                                 @Id,
                                 @MerchantId,
                                 @ExternalId,
                                 @CustomerId,
                                 @Amount,
                                 @Currency,
                                 @RequestId,
                                 @IpAddress,
                                 @SecureSuccessToken,
                                 @SecureFailureToken,
                                 @JobId,
                                 @ExpiresInSeconds,
                                 @Phone,
                                 @Email,
                                 @EventDetails,
                                 @EventType,
                                 @OrderStatus,
                                 @Description,
                                 @CreatedAt
                             )
                     """;

        var model = new OrderClickhouseModel
        {
            Id = order.Id.ToString(),
            MerchantId = order.MerchantId.ToString(),
            ExternalId = order.MerchantId.ToString(),
            CustomerId = order.CustomerId.ToString(),
            Amount = order.Amount,
            Currency = order.Currency.ToString(),
            RequestId = orderEvent.RequestId ?? "",
            IpAddress = orderEvent.IpAddress,
            SecureSuccessToken = order.SecureSuccessToken.ToString(),
            SecureFailureToken = order.SecureFailureToken.ToString(),
            JobId = order.JobId.ToString(),
            ExpiresInSeconds = order.ExpiresInSeconds,
            Phone = order.Phone,
            Email = order.Email,
            EventDetails = orderEvent.EventDetails,
            EventType = orderEvent.EventType.ToString(),
            OrderStatus = orderEvent.OrderStatus.ToString(),
            Description = orderEvent.Description,
            CreatedAt = order.CreatedAt,
        };


        var result = await context.ExecuteAsync(query, model, cancellationToken);
        if (result.IsSuccess)
        {
            logger.LogInformation("{Object}: Successfully Inserted Order {OrderId} to clickhouse with Job {JobId} and request id {RequestId} sending to clickhouse at {CreatedAt}",
                nameof(ClickhouseInserter), orderId, order.JobId, orderEvent.RequestId, DateTimeOffset.UtcNow);
        }

        else
        {
            logger.LogError("{Object}: Failed to Insert Order {OrderId} to clickhouse with Job {JobId} and request id {RequestId} sending to clickhouse at {CreatedAt}", nameof(ClickhouseInserter),
                orderId, order.JobId, orderEvent.RequestId, DateTimeOffset.UtcNow);
            throw new Exception(result.Error.Message);
        }
    }
}
