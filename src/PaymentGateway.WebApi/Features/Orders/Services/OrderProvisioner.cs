using Hangfire.Server;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Payments.Services;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public record OrderCheckMessage(string RequestId, string IpAddress, MerchantId MerchantId, CustomerId CustomerId, ExternalId ExternalId);

public sealed class OrderProvisioner(ILogger<OrderProvisioner> logger, IOrderUpserter orderUpserter, ITabadulOrderService tabadulOrderService, IHangfireJobQueueService jobQueueService)
    : IOrderProvisioner
{
    public async Task ProvisionAsync(Guid orderId, OrderCheckMessage message, CancellationToken ct, PerformContext? context = null)
    {
        var retryCount = context?.GetJobParameter<int>("RetryCount") ?? 0;
        var jobId = context?.BackgroundJob.Id ?? "";
        logger.LogInformation("OrderProvisioner: Provisioning Order {OrderId} with RequestId {RequestId} and JobId {JobId} at {CreatedAt} RetryCount {RetryCount}", orderId, message.RequestId, jobId,
            DateTimeOffset.UtcNow, retryCount);
        var findOrderStatus = await tabadulOrderService.FindOrderStatus(orderId, message.MerchantId, message.RequestId, ct);
        if (findOrderStatus.IsFailure)
        {
            if (findOrderStatus.Error.Number == (int)TabadulErrorCode.OrderDeclinedCredentialsError)
            {
                await orderUpserter.AddFailurePaymentEventAsync(orderId, findOrderStatus.Error, message.RequestId, message.IpAddress, true, ct);
                var notificationMessage = CreateNotificationMessage(orderId, message, OrderStatuses.FailedNoRetry);

                jobQueueService.Enqueue<IMerchantNotifier>(job => job.ProcessOrderPaymentNotification(orderId, notificationMessage, ct, null));
                return;
            }

            logger.LogError("OrderProvisioner: Order not found {OrderId}, with RequestId {RequestId} ", orderId, message.RequestId);
            throw new Exception(findOrderStatus.Error.Message);
        }

        var order = findOrderStatus.Data;
        if (order.OrderStatus != TabadulOrderStatuses.DepositedSuccessfully)
        {
            logger.LogError("OrderProvisioner: Order {OrderId} not paid yet with RequestId {RequestId} ", orderId, message.RequestId);
            throw new Exception("Order Not paid yet");
        }

        await orderUpserter.AddSuccessPaymentEventAsync(orderId, findOrderStatus.Data, message.RequestId, message.IpAddress, true, ct);
        logger.LogError("OrderProvisioner: Order {OrderId} paid successfully with RequestId {RequestId} ", orderId, message.RequestId);

        var successNotificationMessage = CreateNotificationMessage(orderId, message, OrderStatuses.Successful);
        jobQueueService.Enqueue<IMerchantNotifier>(job => job.ProcessOrderPaymentNotification(orderId, successNotificationMessage, ct, null));
    }

    private static OrderNotificationMessage CreateNotificationMessage(Guid orderId, OrderCheckMessage message, OrderStatuses orderStatus) =>
        new(orderId, message.ExternalId, message.CustomerId, message.RequestId, message.IpAddress, message.MerchantId, orderStatus);
}
