using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public sealed class OpenedOrderDuplicatedBlocker(IOrderEventRepository orderRepository) : IOpenedOrderDuplicatedBlocker
{
    public async Task<Result<bool>> HasOpenedOrderAsync(CustomerId customerId, MerchantId merchantId, CancellationToken ct)
    {
        var result = await orderRepository.GetQueryable()
            .Where(x => x.Order!.CustomerId == customerId && x.Order.MerchantId == merchantId && OrderHelper.OpenedOrderStatuses.Contains(x.OrderStatus))
            .AnyAsync(ct);

        return Result<bool>.Success(result);
    }
}
