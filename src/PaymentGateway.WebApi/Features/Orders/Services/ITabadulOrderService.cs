using PaymentGateway.Integrations.Services.Tabadul.OrderGetStatusModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderRegisterModels;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Payments.OrderPay;

namespace PaymentGateway.WebApi.Features.Orders.Services;

public interface ITabadulOrderService
{
    Task<Result<TabadulOrderRegisterResponse>> RegisterOrderAsync(Order order, string? returnUrl = null, CancellationToken cancellationToken = default);
    Task<Result<OrderGetStatusResponse>> FindOrderStatus(Guid orderId, MerchantId merchantId, string requestId, CancellationToken cancellationToken = default);
    // Task<Result<TabadulOrderPaymentResponse>> PayToTabadulAsync(OrderPayRequest request, CancellationToken cancellationToken);
}
