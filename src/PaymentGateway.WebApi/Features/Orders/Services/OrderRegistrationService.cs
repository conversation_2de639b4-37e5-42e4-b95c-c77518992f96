using AutoMapper;
using PaymentGateway.Integrations.Services.Tabadul.OrderRegisterModels;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderRegister;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.Services;

/// <summary>
///     Encapsulates the shared order registration logic used by different endpoints.
/// </summary>
public interface IOrderRegistrationService
{
    /// <summary>
    ///     Registers a new order (calls external system, checks duplication, etc.).
    /// </summary>
    /// <param name="request">The incoming request data (with all common fields).</param>
    /// <param name="saveChanges">persist the data directly to the database</param>
    /// <param name="returnUrl"> The return URL to be used in the external service.</param>
    /// <param name="ct"></param>
    /// <returns>A Result containing either the created order or an error.</returns>
    Task<Result<(Order order, string url)>> RegisterOrderAsync(OrderRegisterBaseRequest request, bool saveChanges, string? returnUrl = null, CancellationToken ct = default);
}

public sealed class OrderRegistrationService(
    IMapper mapper,
    IRequestIdResolver requestIdResolver,
    ILogger<IOrderRegistrationService> logger,
    ITabadulOrderService tabadulOrderService,
    IClaimsExtractor claimExtractor,
    IMerchantConfigRepository merchantConfigRepository,
    IOpenedOrderDuplicatedBlocker openedOrderDuplicatedBlocker,
    OrderConfiguration orderConfigurations) : IOrderRegistrationService
{
    

    
    public async Task<Result<(Order order, string url)>> RegisterOrderAsync(OrderRegisterBaseRequest request, bool saveChanges, string? returnUrl = null, CancellationToken ct = default)
    {
        var requestId = requestIdResolver.GetRequestId();

        var result = TryGetMerchantIdFromClaims(requestId);
        if (result.IsFailure) return Result<(Order order, string url)>.Failure(result.Error);
        var merchantId = result.Data;

        var tryGetMerchantConfig = await TryGetMerchantConfig(ct, merchantId);
        if (tryGetMerchantConfig.IsFailure) return Result<(Order order, string url)>.Failure(tryGetMerchantConfig.Error);
        var merchantConfig = tryGetMerchantConfig.Data;
        

        var orderCreationResult = CreateOrderEntity(request, merchantId, requestId);
        if (orderCreationResult.IsFailure)
            return Result<(Order, string)>.Failure(orderCreationResult.Error);
        var order = orderCreationResult.Data;

        var checkIfOrderIdDuplicated = await CheckIfOrderIdDuplicated(merchantConfig, order, ct);
        if (checkIfOrderIdDuplicated.IsFailure) return Result<(Order, string)>.Failure(checkIfOrderIdDuplicated.Error);

        var registerOrderResult = await TryToRegisterOrderOnTabadulAsync(order,requestId,returnUrl,ct);
        if (registerOrderResult.IsFailure) return Result<(Order, string)>.Failure(registerOrderResult.Error);
        
        AddAdditionalPropertiesToOrder(order, registerOrderResult, requestId);

        return Result<(Order, string)>.Success((order, registerOrderResult.Data.FormUrl!));
    }

    private async Task<Result<TabadulOrderRegisterResponse>> TryToRegisterOrderOnTabadulAsync(Order order,string requestId, string? returnUrl, CancellationToken ct)
    {
        var externalResult = await tabadulOrderService.RegisterOrderAsync(order, returnUrl, ct);
        if (externalResult.IsSuccess) return Result<TabadulOrderRegisterResponse>.Success(externalResult.Data);
        logger.LogError("Failed to create order with external service, requestId: {requestId}, error: {error}", requestId, externalResult.Error);
        return Result<TabadulOrderRegisterResponse>.Failure(externalResult.Error);

    }
    private async Task<Result> CheckIfOrderIdDuplicated(MerchantConfig merchantConfig, Order order,CancellationToken ct)
    {
        if (merchantConfig is not { PreventDuplicateCustomerOrders: true, }) return Result.Success();
        var existingOrder = await openedOrderDuplicatedBlocker.HasOpenedOrderAsync(order.CustomerId, order.MerchantId, ct);
        return existingOrder is
        {
            IsSuccess: true,
            Data: true
        } ? Result<(Order, string)>.Failure(OrderErrors.OpenedOrderDetected) : Result.Success();
    }
    private Result<MerchantId> TryGetMerchantIdFromClaims(string requestId)
    {
        var merchantIdValue = claimExtractor.GetMerchantIdAsString();
        if (merchantIdValue == null)
        {
            const string errorMsg = "MerchantId is missing in the claims.";
            logger.LogError("{Error}, requestId: {requestId}", errorMsg, requestId);
            return Result<MerchantId>.Failure(OrderErrors.MerchantIdMissing);
        }
        var merchantId = MerchantId.From(merchantIdValue);
        return Result<MerchantId>.Success(merchantId);
    }
    
    private void AddAdditionalPropertiesToOrder(Order order, Result<TabadulOrderRegisterResponse> externalResult, string requestId)
    {
        order.Id = OrderId.From(externalResult.Data.OrderId);
        order.CreatedAt = DateTimeOffset.UtcNow;
        logger.LogInformation("Order created successfully, requestId: {requestId}", requestId);
    }

    private async Task<Result<MerchantConfig>> TryGetMerchantConfig(CancellationToken ct, MerchantId merchantId)
    {
        var merchantConfig = await merchantConfigRepository.FindAsync(x => x.MerchantId == merchantId, ct);
        return merchantConfig is null ? Result<MerchantConfig>.Failure(OrderErrors.MerchantConfigIsMissing): Result<MerchantConfig>.Success(merchantConfig); 
        
    }

    private Result<Order> CreateOrderEntity(OrderRegisterBaseRequest req, MerchantId merchantId, string requestId)
    {
        var order = mapper.Map<Order>(req);
        order.SecureSuccessToken = Ulid.NewUlid();
        order.SecureFailureToken = Ulid.NewUlid();

        order.CustomerId = GetCustomerId(req.CustomerId);
        order.MerchantId = merchantId;

        var externalIdResult = AssignOrGenerateExternalId(req, order, requestId);
        if (externalIdResult.IsFailure) return Result<Order>.Failure(externalIdResult.Error);
        order.ExpiresInSeconds = GetExpiresInSecond(req.ExpiresInSeconds);
        return Result<Order>.Success(order);
    }

    private static CustomerId GetCustomerId(string? customerId) =>
        string.IsNullOrWhiteSpace(customerId) ? CustomerId.From(AppConstants.DefaultCustomerId) : CustomerId.From(customerId);

    private static Result AssignOrGenerateExternalId(OrderRegisterBaseRequest req, Order order, string requestId)
    {
        var externalId = string.IsNullOrWhiteSpace(req.ExternalId)
            ? Guid.NewGuid()
                .ToString()
                .Replace("-", string.Empty)
            : req.ExternalId;

        order.ExternalId = ExternalId.From(externalId);
        return Result.Success();
    }

    private int GetExpiresInSecond(int requestedExpiresInSeconds) => requestedExpiresInSeconds > 0 ? requestedExpiresInSeconds : orderConfigurations.ExpiresInSeconds;
}
