using PaymentGateway.Integrations.Services.Tabadul;

namespace PaymentGateway.WebApi.Features.Orders.OrderRegister;

/// <summary>
/// The request payload for registering an order.
/// </summary>
public record OrderRegisterRequest(
    // These parameters match the constructor of OrderCommonRequest
    string? ExternalId,
    long Amount,
    CurrencyIso4217 Currency,
    string? CustomerId,
    string? Phone,
    string? Email,
    int ExpiresInSeconds) : OrderRegisterBaseRequest(ExternalId, Amount, Currency, CustomerId, Phone, Email, ExpiresInSeconds);