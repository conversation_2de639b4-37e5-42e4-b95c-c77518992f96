using System.Text.RegularExpressions;
using FastEndpoints;
using FluentValidation;
using PaymentGateway.Integrations.Extensions;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.OrderRegister;

/// <summary>
///     Holds the set of properties that both OrderRegisterRequest and OrderUiRegisterRequest share.
/// </summary>
public record OrderRegisterBaseRequest(string? ExternalId, long Amount, CurrencyIso4217 Currency, string? CustomerId, string? Phone, string? Email, int ExpiresInSeconds);

/// <summary>
///     Base validator for all OrderRegister-based requests.
/// </summary>
/// <typeparam name="TRequest">A request type that inherits <see cref="OrderRegisterBaseRequest" />.</typeparam>
public abstract class OrderRegisterBaseRequestValidator<TRequest> : Validator<TRequest> where TRequest : OrderRegisterBaseRequest
{
    protected OrderRegisterBaseRequestValidator()
    {
        // Common Rule: Amount
        RuleFor(x => x.Amount)
            .GreaterThan(0)
            .WithErrorCode(OrderErrors.OrderAmountMustBeGreaterThanZero.Number.ToString())
            .WithMessage(OrderErrors.OrderAmountMustBeGreaterThanZero.Message);

        // Common Rule: Currency
        RuleFor(x => x.Currency)
            .NotNull()
            .Must(x => Enum.IsDefined(typeof(CurrencyIso4217), x))
            .WithErrorCode(OrderErrors.OrderCurrencyMustBeValid.Number.ToString())
            .WithMessage(OrderErrors.OrderCurrencyMustBeValid.Message);

        // Common Rule: Phone
        RuleFor(x => x.Phone)
            .Must(x => Regex.Match(x!, AppConstants.Regex.PhonePattern)
                .Success)
            .When(x => x.Phone.IsNotNullOrWhiteSpace())
            .WithErrorCode(OrderErrors.OrderPhoneMustBeStartWith07.Number.ToString())
            .WithMessage(OrderErrors.OrderPhoneMustBeStartWith07.Message);

        RuleFor(x => x.Email)
            .Must(EmailIsValid)
            .WithErrorCode(OrderErrors.OrderEmailMustBeValid.Number.ToString())
            .WithMessage(OrderErrors.OrderEmailMustBeValid.Message);

        RuleFor(x => x.CustomerId)
            .Must(CustomerIdIsValid)
            .When(x => x.CustomerId != null)
            .WithErrorCode(OrderErrors.CustomerIdIsNotValid.Number.ToString());
    }

    private static bool CustomerIdIsValid(string? x) => x switch
    {
        null => true,
        {
            Length: > 255 or < 5,
        } => false,
        _ => true,
    };
    
    private static bool EmailIsValid(string? x) => x switch
    {
        null => true,
        {
            Length: > 100 or < 5,
        } => false,
        _ => Regex.Match(x, AppConstants.Regex.EmailPattern)
            .Success
    };
}
