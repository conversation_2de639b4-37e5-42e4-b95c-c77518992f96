// using FastEndpoints;
// using PaymentGateway.WebApi.Common;
// using PaymentGateway.WebApi.Common.Services;
// using PaymentGateway.WebApi.Features.Orders.Services;
//
// namespace PaymentGateway.WebApi.Features.Orders.OrderRegister;
//
// public sealed class OrderRegisterEndpoint(
//     IClickhouseTaskBuilder clickhouseTaskBuilder,
//     IOrderUpserter orderUpserter,
//     IOrderRegistrationService orderRegistrationService,
//     IIpResolver ipResolver,
//     IRequestIdResolver requestIdResolver) : Endpoint<OrderRegisterRequest, IResult>
// {
//     public override void Configure()
//     {
//         Post(AppRoute.Orders.Register);
//         AuthSchemes(AppConstants.SecuritySchemes.BasicSchemeName);
//     }
//
//     public override async Task HandleAsync(OrderRegisterRequest req, CancellationToken ct)
//     {
//         var result = await orderRegistrationService.RegisterOrderAsync(req, true, null, ct);
//         if (result.IsFailure)
//         {
//             await Send.ResultAsync(result.Error.Problem());
//             return;
//         }
//
//         var (order, _) = result.Data;
//         var requestId = requestIdResolver.GetRequestId();
//         var ip = ipResolver.GetIp();
//         await orderUpserter.CreateOrderAsync(order, requestId, ip, true, AppConstants.OrderEventDescriptions.OrderCreated, ct);
//         clickhouseTaskBuilder.SendToClickhouse(req, ip, requestId, order, ct);
//         await SendAsync(Results.Created(AppRoute.Orders.FullPath, result.Data.order.Id), 201, ct);
//     }
// }
