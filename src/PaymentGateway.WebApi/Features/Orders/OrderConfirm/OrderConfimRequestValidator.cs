using FastEndpoints;
using FluentValidation;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.OrderConfirm;

public class OrderConfirmRequestValidator : Validator<OrderConfirmRequest>
{
    public OrderConfirmRequestValidator()
    {
        RuleFor(x => x.StatusToken)
            .Must(x => Ulid.TryParse(x, out _))
        .WithErrorCode(OrderErrors.OrderStatusTokenIsInvalid.Number.ToString())
        .WithMessage(OrderErrors.OrderStatusTokenIsInvalid.Message);
    }
}