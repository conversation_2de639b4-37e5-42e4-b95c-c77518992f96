using FastEndpoints;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Orders.Shared;
using PaymentGateway.WebApi.Features.Payments.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace PaymentGateway.WebApi.Features.Orders.OrderConfirm;

public sealed class OrderConfirmEndpoint(
    IClickhouseTaskBuilder clickhouseTaskBuilder,
    IOrderRepository orderRepository,
    IOrderEventRepository orderEventRepository,
    IRequestIdResolver requestIdResolver,
    ILogger<OrderConfirmEndpoint> logger,
    IClaimsExtractor claimExtractor,
    IHangfireJobQueueService jobQueueService,
    IIpResolver ipResolver) : Endpoint<OrderConfirmRequest, IResult>
{
    public override void Configure()
    {
        Post(AppRoute.Orders.Confirm);
        AuthSchemes(AppConstants.SecuritySchemes.ApiKeySchemeName);
    }

    public override async Task HandleAsync(OrderConfirmRequest req, CancellationToken ct)
    {
        if (!Ulid.TryParse(req.StatusToken, out var statusToken))
        {
            await Send.ResultAsync(OrderErrors.InvalidStatusToken.Problem());
            return;
        }

        var requestId = requestIdResolver.GetRequestId();
        var ipAddress = ipResolver.GetIp();

        var merchantIdValue = claimExtractor.GetMerchantIdAsString();
        if (merchantIdValue == null)
        {
            logger.LogError("MerchantId is missing in the claims, requestId: {RequestId}", requestId);
            await Send.ResultAsync(OrderErrors.MerchantIdMissing.Problem());
            return;
        }

        var order = await orderRepository.FindAsync(x => x.MerchantId == MerchantId.From(merchantIdValue) && (x.SecureFailureToken == statusToken || x.SecureSuccessToken == statusToken), ct);

        if (order is null)
        {
            await Send.NotFoundAsync(ct);
            return;
        }

        var isOrderClosed = await orderEventRepository.OrderIsClosedAsync(order.Id, ct);
        if (isOrderClosed)
        {
            await Send.ResultAsync(OrderErrors.OrderAlreadyClosed.Problem());
            return;
        }

        if (statusToken == order.SecureSuccessToken)
            await OrderPaidSuccessfulProcess(order, requestId, statusToken, ipAddress, ct);
        else if (statusToken == order.SecureFailureToken) await OrderPaidFailedProcess(order, requestId, statusToken, ipAddress, ct);

        TerminateOrderProvisionBackgroundTask(order, requestId);
        clickhouseTaskBuilder.SendToClickhouse(req, ipAddress, requestId, order, ct);
        await Send.OkAsync(ct);
    }

    private void TerminateOrderProvisionBackgroundTask(Order order, string requestId)
    {
        var deleted = jobQueueService.Delete(order.JobId.ToString());
        if (deleted) logger.LogInformation("Job {jobId} deleted for order {orderId}, requestId: {RequestId}", order.JobId, order.Id, requestId);
    }

    private async Task OrderPaidSuccessfulProcess(Order order, string requestId, Ulid statusToken, string ipAddress, CancellationToken ct)
    {
        const OrderStatuses successOrderStatus = OrderStatuses.Successful;
        const EventTypes successEventType = EventTypes.OrderPaymentSuccessful;
        logger.LogInformation("{Object}: Order {orderId} payment successful using {token}, requestId: {RequestId}", nameof(OrderConfirmEndpoint), order.Id, requestId, statusToken);
        await AddOrderEventAsync(order.Id, successEventType, successOrderStatus, AppConstants.OrderEventDescriptions.OrderConfirmSuccessful, requestId, ipAddress, ct);
        NotifyMerchant(order, requestId, ipAddress, ct, successOrderStatus);
    }

    private async Task OrderPaidFailedProcess(Order order, string requestId, Ulid statusToken, string ipAddress, CancellationToken ct)
    {
        const OrderStatuses failedOrderStatus = OrderStatuses.FailedNoRetry;
        const EventTypes eventType = EventTypes.OrderPaymentFailed;
        logger.LogInformation("{Object}: Order {orderId} payment failed using {token}, requestId: {RequestId}", nameof(OrderConfirmEndpoint), order.Id, requestId, statusToken);
        await AddOrderEventAsync(order.Id, eventType, failedOrderStatus, AppConstants.OrderEventDescriptions.OrderPaymentFailed, requestId, ipAddress, ct);
        NotifyMerchant(order, requestId, ipAddress, ct, failedOrderStatus);
    }

    private void NotifyMerchant(Order order, string requestId, string ipAddress, CancellationToken ct, OrderStatuses successOrderStatus)
    {
        var orderNotificationMessage = CreateOrderNotificationMessage(order, requestId, ipAddress, successOrderStatus);
        var jobId = jobQueueService.Enqueue<IMerchantNotifier>(x => x.ProcessOrderPaymentNotification(order.Id.Value, orderNotificationMessage, ct, null));
        logger.LogInformation("{Object}: RequestId {RequestId} for Order {OrderId} started job {JobId} to notify merchant {MerchantId} the OrderStatus {OrderStatus} at {CreatedAt}",
            nameof(OrderConfirmEndpoint), requestId, order.Id, jobId, order.MerchantId, successOrderStatus, DateTimeOffset.UtcNow);
    }

    private static OrderNotificationMessage CreateOrderNotificationMessage(Order order, string requestId, string ipAddress, OrderStatuses successOrderStatus)
    {
        var orderNotificationMessage = new OrderNotificationMessage(order.Id.Value, order.ExternalId, order.CustomerId, requestId, ipAddress, order.MerchantId, successOrderStatus);
        return orderNotificationMessage;
    }


    private async Task AddOrderEventAsync(OrderId orderId, EventTypes eventType, OrderStatuses orderStatus, string description, string requestId, string ipAddress, CancellationToken ct)
    {
        var orderEvent = new OrderEvent
        {
            OrderId = orderId,
            EventType = eventType,
            OrderStatus = orderStatus,
            Description = description,
            EventDetails = "null",
            CreatedAt = DateTimeOffset.UtcNow,
            Id = OrderEventId.New(),
            IpAddress = ipAddress,
            RequestId = requestId,
        };

        await orderEventRepository.AddAsync(orderEvent, true, ct);
    }
}
