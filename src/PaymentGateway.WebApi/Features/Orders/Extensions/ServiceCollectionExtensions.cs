using PaymentGateway.Integrations.Services;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Services;
// using PaymentGateway.WebApi.Features.Payments.Cards.Data;

namespace PaymentGateway.WebApi.Features.Orders.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddOrderServices(this IServiceCollection services)
    {
        services.AddScoped<IOrderRepository, OrderRepository>();
        services.AddScoped<IOrderEventRepository, OrderEventRepository>();
        services.AddScoped<IOrderUpserter, OrderUpserter>();
        services.AddScoped<ITabadulOrderService, TabadulOrderService>();
        services.AddScoped<IOpenedOrderDuplicatedBlocker, OpenedOrderDuplicatedBlocker>();
        // services.AddScoped<ICardRepository, CardRepository>();
        services.AddScoped<IMerchantCredentialProvider, MerchantCredentialProvider>();
        services.AddScoped<IOrderRegistrationService, OrderRegistrationService>();
        services.AddScoped<IOrderProvisioner, OrderProvisioner>();
        services.AddScoped<IMerchantTabadulLoginRepository, MerchantTabadulLoginRepository>();
        services.AddScoped<IClickhouseInserter, ClickhouseInserter>();
        services.AddScoped(typeof(IClickhouseTaskBuilder), typeof(ClickhouseTaskBuilder));
        return services;
    }
}
