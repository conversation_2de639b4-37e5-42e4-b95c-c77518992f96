using PaymentGateway.WebApi.Common.Extensions;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.Extensions;

public static class WebApplicationBuilderExtensions
{
    public static WebApplicationBuilder AddOrders(this WebApplicationBuilder builder)
    {
        var orderConfig = builder.Configuration.GetConfiguration<OrderConfiguration>("Application:Order");
        var orderEventConfig = builder.Configuration.GetConfiguration<OrderEventConfigurations>("Application:OrderEvent");
        builder.Services.AddSingleton(orderConfig)
            .AddSingleton(orderEventConfig)
            .AddOrderServices();
        return builder;
    }
    
}