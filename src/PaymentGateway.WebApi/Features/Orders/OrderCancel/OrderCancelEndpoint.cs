using FastEndpoints;
using FluentValidation;
using PaymentGateway.Integrations.Extensions;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.OrderConfirm;
using PaymentGateway.WebApi.Features.Orders.Services;
using PaymentGateway.WebApi.Features.Orders.Shared;
using PaymentGateway.WebApi.Features.Payments.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace PaymentGateway.WebApi.Features.Orders.OrderCancel;

public sealed record OrderCancelRequest(string? OrderId, string? ExternalId);

public sealed class OrderCancelRequestValidator : Validator<OrderCancelRequest>
{
    public OrderCancelRequestValidator()
    {
        RuleFor(x => x.OrderId)
            .Must(x => OrderId.TryParse(x, out _))
            .When(x => x.OrderId.IsNotNullOrWhiteSpace() && x.ExternalId == null)
            .WithErrorCode(OrderErrors.OrderIdIsNotValid.Number.ToString())
            .WithMessage(OrderErrors.OrderIdIsNotValid.Message);

        RuleFor(x => x.ExternalId)
            .Length(32)
            .When(x => x.OrderId == null)
            .WithErrorCode(OrderErrors.ExternalIdMustProvidedWhenOrderIdIsNotProvided.Number.ToString())
            .WithMessage(OrderErrors.ExternalIdMustProvidedWhenOrderIdIsNotProvided.Message);
    }
}

public sealed class OrderCancelEndpoint(
    IClickhouseTaskBuilder clickhouseTaskBuilder,
    IOrderRepository orderRepository,
    IOrderEventRepository orderEventRepository,
    IRequestIdResolver requestIdResolver,
    ILogger<OrderConfirmEndpoint> logger,
    IClaimsExtractor claimExtractor,
    IHangfireJobQueueService jobQueueService,
    ITabadulOrderService tabadulOrderService,
    IIpResolver ipResolver) : Endpoint<OrderCancelRequest, IResult>
{
    public override void Configure()
    {
        Post(AppRoute.Orders.Cancel);
        AuthSchemes(AppConstants.SecuritySchemes.BasicSchemeName);
    }

    public override async Task<IResult> ExecuteAsync(OrderCancelRequest request, CancellationToken ct)
    {
        var requestId = requestIdResolver.GetRequestId();
        if (!OrderId.TryParse(request.OrderId, out var orderId) && request.ExternalId == null)
        {
            logger.LogError("The provided order iD is not valid: {OrderId} , RequestId: {RequestId}", request.OrderId, requestId);
            return OrderErrors.OrderIdIsNotValid.Problem();
        }

        Order? order;
        if (request.ExternalId != null)
        {
            order = await orderRepository.FindAsync(x => x.ExternalId == ExternalId.From(request.ExternalId), ct);
            if (order is null) return Results.NotFound();
            orderId = order.Id;
        }

        var ipAddress = ipResolver.GetIp();

        var merchantIdValue = claimExtractor.GetMerchantIdAsString();
        if (merchantIdValue == null)
        {
            logger.LogError("MerchantId is missing in the claims, requestId: {RequestId}", requestId);
            return OrderErrors.MerchantIdMissing.Problem();
        }

        order = await orderRepository.FindAsync(x => x.MerchantId == MerchantId.From(merchantIdValue) && x.Id == orderId, ct);

        if (order is null) return Results.NotFound();

        var isOrderCanBeCanceled = await orderEventRepository.OrderCanBeCanceledAsync(order.Id, ct);
        if (!isOrderCanBeCanceled) return OrderErrors.OrderCannotCanceled.Problem();

        var orderValidationFromTabadulResult = await ValidateOrderFromTabadul(order, merchantIdValue, requestId, ct);
        if (orderValidationFromTabadulResult.IsFailure) return orderValidationFromTabadulResult.Error.Problem();

        await OrderCancellationProcess(order, requestId, ipAddress, ct);
        TerminateOrderProvisionBackgroundTask(order, requestId);
        clickhouseTaskBuilder.SendToClickhouse(orderId, ipAddress, requestId, order, ct);
        return Results.Ok();
    }

    private async Task<Result> ValidateOrderFromTabadul(Order order, string merchantIdValue, string requestId, CancellationToken ct)
    {
        var orderFromTabadulResult = await tabadulOrderService.FindOrderStatus(order.Id.Value, MerchantId.From(merchantIdValue), requestId, ct);
        if (orderFromTabadulResult.IsFailure)
        {
            logger.LogError("Failed to fetch order status from Tabadul for Order {OrderId}, MerchantId {MerchantId}, RequestId {RequestId}. Error: {ErrorMessage}", order.Id, order.MerchantId,
                requestId, orderFromTabadulResult.Error.Message);
            return OrderErrors.OrderCannotCanceled;
        }

        var orderFromTabadul = orderFromTabadulResult.Data;
        return orderFromTabadul.OrderStatus == TabadulOrderStatuses.OrderRegistered ? Result.Success() : OrderErrors.OrderCannotCanceled;
    }

    private void TerminateOrderProvisionBackgroundTask(Order order, string requestId)
    {
        var deleted = jobQueueService.Delete(order.JobId.ToString());
        if (deleted) logger.LogInformation("Job {JobId} deleted for order {OrderId}, requestId: {RequestId}", order.JobId, order.Id, requestId);
    }

    private async Task OrderCancellationProcess(Order order, string requestId, string ipAddress, CancellationToken ct)
    {
        const OrderStatuses orderStatus = OrderStatuses.Canceled;
        const EventTypes eventType = EventTypes.OrderCanceled;
        logger.LogInformation("{Object}: Order {OrderId} payment canceled , requestId: {RequestId}", nameof(OrderConfirmEndpoint), order.Id, requestId);
        await AddOrderEventAsync(order.Id, eventType, orderStatus, AppConstants.OrderEventDescriptions.OrderCanceled, requestId, ipAddress, ct);
        NotifyMerchant(order, requestId, ipAddress, orderStatus, ct);
    }

    private void NotifyMerchant(Order order, string requestId, string ipAddress, OrderStatuses orderStatus, CancellationToken ct)
    {
        var orderNotificationMessage = CreateOrderNotificationMessage(order, requestId, ipAddress, orderStatus);
        var jobId = jobQueueService.Enqueue<IMerchantNotifier>(x => x.ProcessOrderPaymentNotification(order.Id.Value, orderNotificationMessage, ct, null));
        logger.LogInformation("{Object}: RequestId {RequestId} for order {OrderId} started job {JobId} to notify merchant {MerchantId} the order status {OrderStatus} at {CreatedAt}",
            nameof(OrderConfirmEndpoint), requestId, order.Id, jobId, order.MerchantId, orderStatus, DateTimeOffset.UtcNow);
    }

    private static OrderNotificationMessage CreateOrderNotificationMessage(Order order, string requestId, string ipAddress, OrderStatuses successOrderStatus)
    {
        var orderNotificationMessage = new OrderNotificationMessage(order.Id.Value, order.ExternalId, order.CustomerId, requestId, ipAddress, order.MerchantId, successOrderStatus);
        return orderNotificationMessage;
    }

    private async Task AddOrderEventAsync(OrderId orderId, EventTypes eventType, OrderStatuses orderStatus, string description, string requestId, string ipAddress, CancellationToken ct)
    {
        var orderEvent = new OrderEvent
        {
            OrderId = orderId,
            EventType = eventType,
            OrderStatus = orderStatus,
            Description = description,
            EventDetails = "null",
            CreatedAt = DateTimeOffset.UtcNow,
            Id = OrderEventId.New(),
            IpAddress = ipAddress,
            RequestId = requestId,
        };

        await orderEventRepository.AddAsync(orderEvent, true, ct);
    }
}
