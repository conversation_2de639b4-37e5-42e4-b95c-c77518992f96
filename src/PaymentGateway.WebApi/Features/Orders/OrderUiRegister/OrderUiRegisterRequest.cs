using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Features.Orders.OrderRegister;

namespace PaymentGateway.WebApi.Features.Orders.OrderUiRegister;

public sealed record OrderUiRegisterRequest(string? ExternalId, long Amount, CurrencyIso4217 Currency, string? CustomerId, string? Phone, string? Email, int ExpiresInSeconds, string StatusRedirectUrl)
    : OrderRegisterBaseRequest(ExternalId, Amount, Currency, CustomerId, Phone, Email, ExpiresInSeconds);
