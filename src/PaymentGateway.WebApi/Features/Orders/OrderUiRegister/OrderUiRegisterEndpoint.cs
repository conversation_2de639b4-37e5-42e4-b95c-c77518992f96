using FastEndpoints;
using Microsoft.Extensions.Options;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Configurations;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Orders.Services;
using Order = PaymentGateway.WebApi.Features.Orders.Data.Order;

namespace PaymentGateway.WebApi.Features.Orders.OrderUiRegister;

public sealed class OrderUiRegisterEndpoint(
    IClickhouseTaskBuilder clickhouseTaskBuilder,
    IRequestIdResolver requestIdResolver,
    IOrderUpserter orderUpserter,
    IIpResolver ipResolver,
    PaymentDbContext context,
    ILogger<OrderUiRegisterEndpoint> logger,
    IOrderRegistrationService orderRegistrationService,
    IHangfireJobQueueService jobQueueService,
    IOptions<ProvisionerConfiguration> provisionerConfiguration) : Endpoint<OrderUiRegisterRequest, OrderUiRegisterResponse>
{
    public override void Configure()
    {
        Post(AppRoute.Orders.UiRegister);
        AuthSchemes(AppConstants.SecuritySchemes.BasicSchemeName);
    }

    public override async Task HandleAsync(OrderUiRegisterRequest req, CancellationToken ct)
    {
        var requestId = requestIdResolver.GetRequestId();
        var ip = ipResolver.GetIp();
        await using var transaction = await context.Database.BeginTransactionAsync(ct);
        var result = await orderRegistrationService.RegisterOrderAsync(req, false, req.StatusRedirectUrl, ct);

        if (result.IsFailure)
        {
            await Send.ResultAsync(result.Error.Problem());
            return;
        }

        var (order, redirectUrl) = result.Data;

        // Check feature flag before sending to provisioner service
        if (provisionerConfiguration.Value.EnableProvisionService)
        {
            var jobId = SendOrderToProvisionerService(order, requestId, ip, ct);
            await AppendJobIdToOrderAsync(order, jobId, requestId, ip, ct);
            logger.LogInformation("{Object}: Order {OrderId} registered with RequestId {RequestId} and sent to ProvisionService with JobId {JobId}",
                nameof(OrderUiRegisterEndpoint), order.Id, requestId, jobId);
        }
        else
        {
            // Create order without sending to provisioner service
            await orderUpserter.CreateOrderAsync(order, requestId, ip, true, AppConstants.OrderEventDescriptions.OrderCreated, ct);
            logger.LogInformation("{Object}: Order {OrderId} registered with RequestId {RequestId} (ProvisionService disabled by feature flag)",
                nameof(OrderUiRegisterEndpoint), order.Id, requestId);
        }

        await transaction.CommitAsync(ct);
        clickhouseTaskBuilder.SendToClickhouse(req, ip, requestId, order, ct);
        var response = new OrderUiRegisterResponse
        {
            Id = order.Id.Value.ToString(), RedirectUrl = redirectUrl,
        };
        await Send.ResponseAsync(response, StatusCodes.Status201Created, ct);
    }

    private async Task AppendJobIdToOrderAsync(Order order, string jobId, string requestId, string ip, CancellationToken ct)
    {
        order.JobId = int.Parse(jobId);
        await orderUpserter.CreateOrderAsync(order, requestId, ip, true, AppConstants.OrderEventDescriptions.OrderCreated, ct);
    }

    private string SendOrderToProvisionerService(Order order, string requestId, string ip, CancellationToken ct)
    {
        var jobId = jobQueueService.Schedule<IOrderProvisioner>(
            job => job.ProvisionAsync(order.Id.Value, new OrderCheckMessage(requestId, ip, order.MerchantId, order.CustomerId, order.ExternalId), ct, null),
            TimeSpan.FromSeconds(provisionerConfiguration.Value.ProvisionBeginsInSeconds));
        logger.LogInformation("OrderUiRegisterEndpoint: Order {OrderId} send to OrderProvisioner with JobId {JobId} At {CreatedAt}", order.Id, jobId, DateTimeOffset.UtcNow);
        return jobId;
    }
}
