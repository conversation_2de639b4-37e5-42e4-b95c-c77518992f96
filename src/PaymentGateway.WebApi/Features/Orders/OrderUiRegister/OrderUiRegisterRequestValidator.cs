using System.Text.RegularExpressions;
using FluentValidation;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Orders.OrderRegister;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.OrderUiRegister;

/// <summary>
///     Validator for <see cref="OrderUiRegisterRequest" />.
///     Inherits common rules and adds validation for StatusRedirectUrl.
/// </summary>
public class OrderUiRegisterRequestValidator : OrderRegisterBaseRequestValidator<OrderUiRegisterRequest>
{
    public OrderUiRegisterRequestValidator()
    {
        RuleFor(x => x.StatusRedirectUrl)
            .Must(StatusRedirectUrlIsValid)
            .WithErrorCode(OrderErrors.OrderStatusRedirectUrlMustBeValid.Number.ToString())
            .WithMessage(OrderErrors.OrderStatusRedirectUrlMustBeValid.Message);
    }

    private static bool StatusRedirectUrlIsValid(string x) =>
        x switch
        {
            null => false,
            { Length: > 515 or < 4 } => false,
            _ => Regex.Match(x, AppConstants.Regex.UrlPattern)
                .Success
        };
}
