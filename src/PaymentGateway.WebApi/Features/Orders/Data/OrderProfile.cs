using AutoMapper;
using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.Integrations.Services.Tabadul.OrderPayModels;
using PaymentGateway.Integrations.Services.Tabadul.OrderRegisterModels;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.OrderFind;
using PaymentGateway.WebApi.Features.Orders.OrderRegister;
using PaymentGateway.WebApi.Features.Orders.OrderUiRegister;
using PaymentGateway.WebApi.Features.Orders.Shared;
using PaymentGateway.WebApi.Features.Payments.OrderPay;
using OrderRegisterRequest = PaymentGateway.WebApi.Features.Orders.OrderRegister.OrderRegisterRequest;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public sealed class OrderProfile : Profile
{
    public OrderProfile()
    {
        CreateMap<OrderRegisterRequest, Order>()
            .ForMember(dest => dest.ExternalId, opt => opt.Ignore())
            .ForMember(dest => dest.CustomerId, opt => opt.Ignore());
  CreateMap<OrderRegisterBaseRequest, Order>()
            .ForMember(dest => dest.ExternalId, opt => opt.Ignore())
            .ForMember(dest => dest.CustomerId, opt => opt.Ignore());

        CreateMap<TabadulOrderRegisterResponse, OrderUiRegisterResponse>()
            .ForMember(x => x.Id, opt => opt.MapFrom(src => src.OrderId.ToString()))
            .ForMember(x => x.RedirectUrl, opt => opt.MapFrom(src => src.FormUrl));

        CreateMap<Order, OrderClickhouseModel>()
            .ForMember(x => x.Currency, op => op.MapFrom(s => s.Currency.ToString()));
        CreateMap<OrderClickhouseModel, Order>()
            .ForMember(x => x.Id, opt => opt.MapFrom(src => OrderId.From(src.Id)))
            .ForMember(x => x.ExternalId, opt => opt.MapFrom((OrderClickhouseModel src) => src.ExternalId == null ? (ExternalId?)null : ExternalId.From(src.ExternalId)))
            .ForMember(x => x.CustomerId, opt => opt.MapFrom(src => CustomerId.From(src.CustomerId)))
            .ForMember(x => x.MerchantId, opt => opt.MapFrom(src => MerchantId.From(src.MerchantId)))
            .ForMember(x => x.Currency, opt => opt.MapFrom(src => Enum.Parse<CurrencyIso4217>(src.Currency)))
            .ForMember(x => x.ExpiresInSeconds, opt => opt.MapFrom(src => src.ExpiresInSeconds));

        CreateMap<Order, OrdersDetailsResponse>()
            .ForMember(x=>x.Id,op=>op.MapFrom(sr=>sr.Id.Value));


        CreateMap<OrderDetailsClickhouseModel, OrdersDetailsResponse>();
        CreateMap<TabadulOrderPaymentResponse, OrderPayResponse>();
        CreateMap<TabadulOrderRegisterResponse,OrderUiRegisterResponse>()
            .ForMember(x=>x.Id,op=>op.MapFrom(sr=>sr.OrderId))
            .ForMember(x=>x.RedirectUrl,op=>op.MapFrom(sr=>sr.FormUrl));
    }
}
