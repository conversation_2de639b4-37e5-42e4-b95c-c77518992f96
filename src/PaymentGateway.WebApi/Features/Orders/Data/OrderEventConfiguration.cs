using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public sealed class OrderEventConfiguration : IEntityTypeConfiguration<OrderEvent>
{
    public void Configure(EntityTypeBuilder<OrderEvent> builder)
    {
        builder.HasKey(x => x.Id);
        builder.HasIndex(x => x.CreatedAt);
        builder.HasIndex(x => x.RequestId);
        builder.HasIndex(x => x.EventType);
        builder.HasIndex(x => x.OrderStatus);
        builder.Property(x => x.Id)
            .HasConversion(x => x.Value.ToString(), x => OrderEventId.From(Ulid.Parse(x)));

        builder.Property(x => x.OrderId)
            .HasMaxLength(26)
            .HasConversion(x => x.Value, x => OrderId.From(x));

        builder.Property(x => x.EventType).HasMaxLength(50)
            .HasConversion<string>();
        builder.Property(x => x.OrderStatus).HasMaxLength(50)
            .HasConversion<string>();

        builder.Property(x => x.RequestId)
            .HasMaxLength(50);
        builder.Property(x => x.Description)
            .HasMaxLength(255);
        builder.Property(x => x.EventDetails).HasColumnType("jsonb");
        builder.HasOne(x => x.Order)
            .WithMany()
            .HasForeignKey(x => x.OrderId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
