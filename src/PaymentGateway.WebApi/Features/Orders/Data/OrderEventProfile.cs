using AutoMapper;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public sealed class OrderEventProfile : Profile
{
    public OrderEventProfile()
    {
        CreateMap<OrderEvent, OrderEventClickhouseModel>()
            .ForMember(x=>x.Id, opt=>opt.MapFrom(src=>src.Id.ToString()))
            .ForMember(x=>x.OrderId, opt=>opt.MapFrom(src=>src.OrderId))
            .ForMember(x=>x.EventDetails, opt=>opt.MapFrom(src=>src.EventDetails))
            .ForMember(x=>x.EventType, opt=>opt.MapFrom(src=>src.EventType.ToString()))
            .ForMember(x=>x.OrderStatus, opt=>opt.MapFrom(src=>src.OrderStatus.ToString()));
        
        CreateMap<OrderEventClickhouseModel, OrderEvent>()
            .ForMember(x => x.Id, opt => opt.MapFrom(src => OrderEventId.From(Ulid.Parse(src.Id))))
            .ForMember(x => x.OrderId, opt => opt.MapFrom(src => src.OrderId))
            .ForMember(x => x.EventType, opt => opt.MapFrom(src => Enum.Parse<EventTypes>(src.EventType)))
            .ForMember(x => x.OrderStatus, opt => opt.MapFrom(src => Enum.Parse<OrderStatuses>(src.OrderStatus)))
            .ForMember(x => x.EventDetails, opt => opt.MapFrom(src => src.EventDetails))
            .ForMember(x => x.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(x => x.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt));
    }

}
