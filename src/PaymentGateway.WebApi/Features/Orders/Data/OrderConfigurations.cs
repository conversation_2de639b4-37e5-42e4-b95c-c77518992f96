using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PaymentGateway.WebApi.Features.Merchants.Data;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public sealed class OrderConfigurations : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.HasIndex(x => x.ExternalId).IsUnique();
        builder.HasIndex(x => x.CustomerId);
        builder.HasIndex(x => x.MerchantId);
        builder.HasIndex(x => x.SecureSuccessToken);
        builder.HasIndex(x => x.SecureFailureToken);
        builder.HasIndex(x => x.JobId);
        
        builder.Property(x => x.Amount).IsRequired();
        builder.Property(x => x.Currency).IsRequired();
        builder.Property(x => x.ExpiresInSeconds).IsRequired();
        builder.Property(x => x.CreatedAt).IsRequired();
        builder.Property(x => x.Phone).HasMaxLength(15);
        builder.Property(x => x.Email).HasMaxLength(100);
        builder.Property(x => x.ExternalId).HasMaxLength(32);
        builder.Property(x => x.MerchantId).HasMaxLength(50).HasConversion(v => v.Value, v => MerchantId.From(v));
        builder.Property(x => x.ExternalId).HasMaxLength(50).HasConversion(v => v.Value, v => ExternalId.From(v));
        builder.Property(x => x.CustomerId).HasMaxLength(255).HasConversion(v => v.Value, v => CustomerId.From(v));
        builder.Property(x => x.Currency).HasConversion<string>();

        builder.Property(x => x.Id)
            .HasMaxLength(26)
            .HasConversion(x => x.Value, x => OrderId.From(x));
        
        builder.Property(x => x.SecureSuccessToken)
            .HasMaxLength(26)
            .HasConversion(x => x.ToString(), x => Ulid.Parse(x));
        
        builder.Property(x => x.SecureFailureToken)
            .HasMaxLength(26)
            .HasConversion(x => x.ToString(), x => Ulid.Parse(x));
            
    }
}
