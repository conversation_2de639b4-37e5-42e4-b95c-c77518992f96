using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Features.Merchants.Data;
using Vogen;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public sealed class Order : EntityBase<OrderId>
{
    public MerchantId MerchantId { get; set; }
    public ExternalId ExternalId { get; set; }
    public long Amount { get; init; }
    public CurrencyIso4217 Currency { get; init; }
    public CustomerId CustomerId { get; set; }
    public int ExpiresInSeconds { get; set; }
    public string? Phone { get; init; }
    public string? Email { get; init; }
    public DateTimeOffset CreatedAt { get; set; }
    public required Ulid SecureSuccessToken { get; set; }
    public required Ulid SecureFailureToken { get; set; }
    public int JobId { get; set; }
}

[ValueObject<Guid>]
public partial struct OrderId;

[ValueObject<string>]
public partial struct CustomerId;

[ValueObject<string>]
public partial struct ExternalId;
