using System.Text.Json.Serialization;
using PaymentGateway.WebApi.Common;
using Vogen;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public sealed class OrderEvent : EntityBase<OrderEventId>
{
    public required OrderId OrderId { get; init; }
    public Order? Order { get; init; }
    public required string EventDetails { get; init; }
    public required EventTypes EventType { get; init; }
    public required OrderStatuses OrderStatus { get; init; }
    public required DateTimeOffset CreatedAt { get; init; }
    public required string? IpAddress { get; init; }
    public required string? RequestId { get; init; }
    public required string? Description { get; init; }
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum EventTypes
{
    OrderCreated,
    OrderPaymentSuccessful,
    OrderPaymentFailed,
    OrderExpired,
    OrderReversed,
    OrderRefunded,
    MerchNotifyFailed,
    MerchNotifySuccessful,
    OrderCanceled
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum OrderStatuses
{
    Initiated,
    Approved,
    InProgress3DS,
    FailedCanRetry,
    FailedNoRetry,
    TimedOut,
    Successful,
    Reversed,
    Refunded,
    Canceled
}

[ValueObject<Ulid>(conversions: Conversions.EfCoreValueConverter)]
public partial struct OrderEventId
{
    public static OrderEventId New() => From(Ulid.NewUlid());
}

public static class OrderHelper
{
    public static OrderStatuses[] OpenedOrderStatuses => [OrderStatuses.Initiated, OrderStatuses.Approved, OrderStatuses.FailedCanRetry, OrderStatuses.InProgress3DS,];
    public static OrderStatuses [] CanBeCanceledStatuses => [OrderStatuses.Initiated,];
 }
