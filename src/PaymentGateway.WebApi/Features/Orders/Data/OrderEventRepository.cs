using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Data;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public interface IOrderEventRepository : IRepository<OrderEvent, OrderEventId>
{
    Task<bool> OrderIsClosedAsync(OrderId orderId, CancellationToken cancellationToken);
    Task<bool> OrderCanBeCanceledAsync(OrderId orderId, CancellationToken cancellationToken);
    Task<bool> MerchantIsAlreadyNotifiedAsync(Guid orderId,OrderStatuses orderStatus, CancellationToken cancellationToken);
}

public sealed class OrderEventRepository(PaymentDbContext context) : Repository<OrderEvent, OrderEventId>(context), IOrderEventRepository
{
    public async Task<bool> OrderIsClosedAsync(OrderId orderId, CancellationToken cancellationToken)
    {
        var result = await GetQueryable()
            .OrderByDescending(x => x.Id)
            .Take(1)
            .Where(x => x.OrderId == orderId)
            .Where(x => !OrderHelper.OpenedOrderStatuses.Contains(x.OrderStatus))
            .AnyAsync(cancellationToken);
        return result;
    }

    public async Task<bool> OrderCanBeCanceledAsync(OrderId orderId, CancellationToken cancellationToken)
    {
        var result = await GetQueryable()
            .OrderByDescending(x => x.Id)
            .Take(1)
            .Where(x => x.OrderId == orderId)
            .Where(x => OrderHelper.CanBeCanceledStatuses.Contains(x.OrderStatus))
            .AnyAsync(cancellationToken);
        return result;
    }

    public async Task<bool> MerchantIsAlreadyNotifiedAsync(Guid orderId,OrderStatuses orderStatus, CancellationToken cancellationToken)
    {
        var result = await GetQueryable()
            .Where(x => x.OrderId == OrderId.From(orderId))
            .Where(x => x.EventType == EventTypes.MerchNotifySuccessful)
            .Where(x => x.OrderStatus == orderStatus)
            .AnyAsync(cancellationToken);
        return result;
    }
}
