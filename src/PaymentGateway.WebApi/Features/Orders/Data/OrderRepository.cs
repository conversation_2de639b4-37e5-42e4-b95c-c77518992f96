using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Orders.OrderFind;

namespace PaymentGateway.WebApi.Features.Orders.Data;

public interface IOrderRepository:IRepository<Order,OrderId>
{
    Task<OrdersDetailsResponse?> FindAsync(OrderId orderId, CancellationToken cancellationToken);
}

public sealed class OrderRepository(PaymentDbContext context,IMapper mapper) : Repository<Order,OrderId>(context), IOrderRepository
{
    public async Task<OrdersDetailsResponse?> FindAsync(OrderId orderId, CancellationToken cancellationToken)
    {

        var result = await GetQueryable()
            .Where(x => x.Id == orderId)
            .ProjectTo<OrdersDetailsResponse>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);
        
        return result;
    }

}
