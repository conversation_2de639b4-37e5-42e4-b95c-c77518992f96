namespace PaymentGateway.WebApi.Features.Orders.Shared;

public class OrderEventClickhouseModel
{
    public required string Id { get; set; }
    public required Guid OrderId { get; set; }
    public string? EventDetails { get; set; }
    public required string EventType { get; set; }
    public required string OrderStatus { get; set; }
    public required DateTimeOffset CreatedAt { get; set; }
    public required string? Description { get; set; }
    public required string? IpAddress { get; set; }
    public required string? RequestId { get; set; }
}
