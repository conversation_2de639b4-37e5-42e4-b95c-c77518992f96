using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Orders.Shared;

public class OrderClickhouseModel
{
    public required Guid Id { get; init; }
    public required string MerchantId { get; init; }
    public string? ExternalId { get; init; }
    public required long Amount { get; init; }
    public required string Currency { get; init; }
    public required string CustomerId { get; init; }
    public required int ExpiresInSeconds { get; init; }
    public string? Phone { get; init; }
    public string? Email { get; init; }
    public required string TokenSuccessToken { get; init; }
    public required string TokenFailureToken { get; init; }
    public required DateTimeOffset CreatedAt { get; init; }
}

public class OrderDetailsClickhouseModel: OrderClickhouseModel
{
   public OrderStatuses OrderStatus { get; init; }
   public EventTypes EventType { get; init; }
   public string? EventDetails { get; init; }
   public string? IpAddress { get; init; }
}