using PaymentGateway.WebApi.Common;

namespace PaymentGateway.WebApi.Features.Orders.Shared;

public abstract class OrderErrors
{
    public static Error OrderAmountMustBeGreaterThanZero => Error.Create(ErrorNumbers.OrderErrors.OrderAmountMustBeGreaterThanZero, nameof(OrderAmountMustBeGreaterThanZero),
        "Amount is required and must be greater then 0");
    public static Error OrderCurrencyMustBeValid => Error.Create(ErrorNumbers.OrderErrors.OrderCurrencyMustBeValid, nameof(OrderCurrencyMustBeValid), "Currency is required and must be of type Currency");
    public static Error OrderPhoneMustBeStartWith07 => Error.Create(ErrorNumbers.OrderErrors.OrderPhoneMustBeStartWith07,
        nameof(OrderPhoneMustBeStartWith07), "Phone is optional but must be a valid phone number");
    public static Error OrderEmailMustBeValid => Error.Create(ErrorNumbers.OrderErrors.OrderEmailMustBeValid, nameof(OrderEmailMustBeValid), "Email is optional but must be a valid email address");
    public static Error OrderIdIsNotValid => Error.Create(ErrorNumbers.OrderErrors.OrderIdIsNotValid, nameof(OrderIdIsNotValid), "The provided Order ID is not valid.");
    public static Error MerchantIdMissing => Error.Create(ErrorNumbers.OrderErrors.MerchantIdMissing, nameof(MerchantIdMissing), "MerchantId can not be resolved");
    public static Error OpenedOrderDetected => Error.Create(ErrorNumbers.OrderErrors.OpenedOrderDetected, nameof(OpenedOrderDetected), "Opened order detected");
    public static Error OrderNotFound => Error.Create(ErrorNumbers.OrderErrors.OrderNotFound, nameof(OrderNotFound), "Order not found", 404);
    public static Error CvvShouldBe3Digits => Error.Create(ErrorNumbers.OrderErrors.CvvShouldBe3Digits, nameof(CvvShouldBe3Digits), "Cvv should be 3 digits");
    public static Error OrderIsAlreadyPaid => Error.Create(ErrorNumbers.OrderErrors.OrderIsAlreadyPaid, nameof(OrderIsAlreadyPaid), "Order is already paid");
    public static Error OrderStatusRedirectUrlMustBeValid => Error.Create(ErrorNumbers.OrderErrors.OrderStatusRedirectUrlMustBeValid, nameof(OrderStatusRedirectUrlMustBeValid),
        "OrderStatusUrl must be valid");
    public static Error OrderAlreadyClosed => Error.Create(ErrorNumbers.OrderErrors.OrderAlreadyClosed, nameof(OrderAlreadyClosed), "Order is already closed", 409);
    public static Error OrderStatusTokenIsInvalid => Error.Create(ErrorNumbers.OrderErrors.OrderStatusTokenIsInvalid, nameof(OrderStatusTokenIsInvalid), "Order status token is invalid");
    public static Error MerchantConfigIsMissing => Error.Create((ErrorNumbers.OrderErrors.MerchantConfigIsMissing), nameof(MerchantConfigIsMissing), "Merchant config is missing");
    public static Error InvalidStatusToken => Error.Create(ErrorNumbers.OrderErrors.InvalidStatusToken, nameof(InvalidStatusToken), "Invalid status token");
    public static Error CustomerIdIsNotValid => Error.Create(ErrorNumbers.OrderErrors.CustomerIdIsNotValid, nameof(CustomerIdIsNotValid), "Customer ID is not valid");
    public static Error OrderCannotCanceled => Error.Create(ErrorNumbers.OrderErrors.OrderCannotCanceled, nameof(OrderCannotCanceled), "Order cannot canceled in its current state");
}
