using FastEndpoints;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Attributes;
using PaymentGateway.WebApi.Features.Orders.Data;
using PaymentGateway.WebApi.Features.Orders.Shared;

namespace PaymentGateway.WebApi.Features.Orders.OrderFind;

public class OrdersDetailsEndpoint(IOrderRepository orderRepository) : EndpointWithoutRequest<OrdersDetailsResponse>
{
    public override void Configure()
    {
        Get(AppRoute.Orders.IdPath);
        AuthSchemes(AppConstants.SecuritySchemes.BasicSchemeName);
        Summary(s =>
        {
            s.Summary = "Get an order by ID.";
            s.Description = "Retrieves the details of an order by its unique identifier.";
            s.Response(200, "Order details returned successfully.");
            s.Response(404, "Order not found.");
            s.Response(400, "Order ID is not valid.");
        });
    }

    public override async Task HandleAsync(CancellationToken cancellationToken)
    {
        var id = Route<string>(AppRoute.IdValue);

        if (!Guid.TryParse(id, out var orderId))
        {
            await Send.ResultAsync(OrderErrors.OrderIdIsNotValid.Problem());
            return;
        }

        var order = await orderRepository.FindAsync(OrderId.From(orderId), cancellationToken);
        if (order is null)
        {
            await Send.NotFoundAsync(cancellationToken);
            return;
        }

        if (order.CustomerId == AppConstants.DefaultCustomerId) order.CustomerId = null;
        await Send.OkAsync(order, cancellationToken);
    }
}
