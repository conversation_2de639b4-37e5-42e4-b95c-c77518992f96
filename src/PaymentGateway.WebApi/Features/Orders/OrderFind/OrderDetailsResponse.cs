using PaymentGateway.Integrations.Services.Tabadul;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Features.Orders.OrderFind;

public class OrdersDetailsResponse
{
    public required Guid Id { get; init; }
    public string? ExternalId { get; init; }
    public required string MerchantId { get; init; }
    public long Amount { get; init; }
    public required CurrencyIso4217 Currency { get; init; }
    public required DateTimeOffset CreatedAt { get; init; }
    public OrderStatuses OrderStatus { get; init; }
    internal EventTypes EventType { get; init; }
    public string? CustomerId { get; set; }
    public string? Phone { get; init; }

    public string? Email { get; init; }

    // todo : this is delayed for the MVP 
    // public CardResponse? Card { get; set; }
    public string? IpAddress { get; init; }
    public int ExpiresInSeconds { get; init; }
    public bool MerchantIsNotified => EventType == EventTypes.OrderPaymentSuccessful;
    public int JobId { get; init; }
}
