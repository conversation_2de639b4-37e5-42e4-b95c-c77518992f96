using PaymentGateway.WebApi.Features.Merchants.Data;

namespace PaymentGateway.WebApi.Features.Merchants.Extensions;

public static class WebApplicationBuilderExtensions
{
    public static WebApplicationBuilder AddMerchants(this WebApplicationBuilder builder)
    {
        builder.Services.AddScoped<IMerchantLoginRepository, MerchantLoginRepository>();
        builder.Services.AddScoped<IMerchantConfigRepository, MerchantConfigRepository>();
        return builder;
    }
}
