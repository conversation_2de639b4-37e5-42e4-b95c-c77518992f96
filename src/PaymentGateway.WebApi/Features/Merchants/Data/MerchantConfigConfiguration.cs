using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public sealed class MerchantConfigConfiguration : IEntityTypeConfiguration<MerchantConfig>
{
    public void Configure(EntityTypeBuilder<MerchantConfig> builder)
    {
        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasMaxLength(26)
            .HasConversion(x => x.Value.ToString(), x => MerchantConfigId.From(Ulid.Parse(x)));
        builder.Property(x => x.MerchantId)
            .HasMaxLength(26)
            .HasConversion(x => x.ToString(), x => MerchantId.From(x));

        builder.HasOne(x => x.Merchant)
            .WithMany()
            .HasForeignKey(x => x.MerchantId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
