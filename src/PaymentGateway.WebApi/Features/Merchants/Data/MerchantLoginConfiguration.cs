using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public class MerchantLoginConfiguration : IEntityTypeConfiguration<MerchantLogin>
{
    public void Configure(EntityTypeBuilder<MerchantLogin> builder)
    {
        builder.HasKey(x => x.Id);
        builder.HasIndex(x => x.ApiKey)
            .IsUnique();
        builder.Property(x => x.Id)
            .HasMaxLength(26)
            .HasConversion(x => x.Value.ToString(), x => Data.MerchantLoginId.From(Ulid.Parse(x)));
       
        builder.Property(x => x.MerchantId)
            .HasMaxLength(26)
            .HasConversion(x => x.ToString(), x => MerchantId.From(x));
        
        builder.Property(x => x.MerchantTabadulLoginId)
            .HasMaxLength(26)
            .HasConversion(x => x.ToString(), x => MerchantTabadulLoginId.From(Ulid.Parse(x)));
       
        builder.Property(x => x.ApiKey).HasMaxLength(255);

        builder.HasOne(x => x.Merchant)
            .WithMany()
            .HasForeignKey(x => x.MerchantId)
            .OnDelete(DeleteBehavior.Restrict);
            
        builder.HasOne(x => x.MerchantTabadulLogin)
            .WithMany(x=>x.MerchantLogins)
            .HasForeignKey(x => x.MerchantTabadulLoginId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
