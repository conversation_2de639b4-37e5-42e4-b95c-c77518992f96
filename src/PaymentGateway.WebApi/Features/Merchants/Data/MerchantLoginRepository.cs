using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Data;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public interface IMerchantLoginRepository
{
    public Task<MerchantLogin?> FindByApiKey(string apiKey);
}

public class MerchantLoginRepository(PaymentDbContext context) : Repository<MerchantLogin, MerchantLoginId>(context), IMerchantLoginRepository
{
    public async Task<MerchantLogin?> FindByApiKey(string apiKey) => await context.MerchantLogins
        .Where(m => m.ApiKey == apiKey)
        .FirstOrDefaultAsync();
}
