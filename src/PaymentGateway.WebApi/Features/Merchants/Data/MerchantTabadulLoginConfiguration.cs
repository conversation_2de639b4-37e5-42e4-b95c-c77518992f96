using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public sealed class MerchantTabadulLoginConfiguration : IEntityTypeConfiguration<MerchantTabadulLogin>
{
    public void Configure(EntityTypeBuilder<MerchantTabadulLogin> builder)
    {
        builder.HasKey(x => x.Id);
        // builder.HasIndex(x => x.TabadulUsername).IsUnique();
        builder.Property(x => x.Id)
            .HasMaxLength(26)
            .HasConversion(x => x.Value.ToString(), x => MerchantTabadulLoginId.From(Ulid.Parse(x)));
    }
}
