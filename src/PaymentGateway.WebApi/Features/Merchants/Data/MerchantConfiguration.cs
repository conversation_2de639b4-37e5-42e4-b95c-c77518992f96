using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public sealed class MerchantConfiguration : IEntityTypeConfiguration<Merchant>
{
    public void Configure(EntityTypeBuilder<Merchant> builder)
    {
        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id)
            .HasMaxLength(26)
            .HasConversion(x => x.Value.ToString(), x => MerchantId.From(x));
        builder.Property(x => x.Name)
            .HasMaxLength(255)
            .IsRequired();
    }
}
