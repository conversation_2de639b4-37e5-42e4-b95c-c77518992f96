using PaymentGateway.WebApi.Common;
using Vogen;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public sealed class MerchantConfig : EntityBase<MerchantConfigId>
{
    public required MerchantId MerchantId { get; set; }
    public Merchant? Merchant { get; set; }
    public required int MaxNotificationRetries { get; init; }
    public required bool PreventDuplicateCustomerOrders { get; init; }
    public required string PublicAPIUrl { get; init; }
}

[ValueObject<Ulid>]
public partial struct MerchantConfigId;