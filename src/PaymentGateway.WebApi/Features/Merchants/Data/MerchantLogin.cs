using PaymentGateway.WebApi.Common;
using Vogen;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public class MerchantLogin : EntityBase<MerchantLoginId>
{
    public MerchantId MerchantId { get; init; }
    public Merchant? Merchant { get; init; }
    public required string ApiKey { get; init; }
    public MerchantTabadulLoginId MerchantTabadulLoginId { get; init; }
    public MerchantTabadulLogin? MerchantTabadulLogin { get; init; }
}

[ValueObject<Ulid>]
public partial struct MerchantLoginId;