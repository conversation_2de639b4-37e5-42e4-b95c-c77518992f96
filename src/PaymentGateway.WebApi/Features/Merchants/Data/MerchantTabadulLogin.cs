using PaymentGateway.WebApi.Common;
using Vogen;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public sealed class MerchantTabadulLogin : EntityBase<MerchantTabadulLoginId>
{
    public required string TabadulUsername { get; init; }
    public required string TabadulPassword { get; init; }
    public ICollection<MerchantLogin>? MerchantLogins { get; set; }
}

[ValueObject<Ulid>]
public partial struct MerchantTabadulLoginId;
