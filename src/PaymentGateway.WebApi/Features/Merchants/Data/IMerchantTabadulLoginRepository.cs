using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Data;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public record MerchantLoginInfo(string MerchantId, string Username, string Password);

public interface IMerchantTabadulLoginRepository : IRepository<MerchantTabadulLogin, MerchantTabadulLoginId>
{
    Task<string?> TryGetMerchantIdAsync(string tabadulUsername, string tabadulPassword);
}

public sealed class MerchantTabadulLoginRepository(PaymentDbContext context, ICachingService cachingService)
    : Repository<MerchantTabadulLogin, MerchantTabadulLoginId>(context), IMerchantTabadulLoginRepository
{
    public async Task<string?> TryGetMerchantIdAsync(string tabadulUsername, string tabadulPassword)
    {
        var cacheKey = $"{AppConstants.CachePrefixes.MerchantTabadulUsernamePrefix}{tabadulUsername}";
        var merchantTabadulLogin = await cachingService.GetAsync<MerchantLoginInfo?>(cacheKey);
        if (merchantTabadulLogin is not null) return merchantTabadulLogin.MerchantId;
        merchantTabadulLogin = await context.MerchantTabadulLogins
            .Where(x => x.TabadulUsername == tabadulUsername)
            .Where(x => x.TabadulPassword == tabadulPassword)
            .Where(x => x.MerchantLogins.Any())
            .SelectMany(x => x.MerchantLogins!)
            .Select(x => new MerchantLoginInfo(x.MerchantId.ToString(), x.MerchantTabadulLogin!.TabadulUsername, x.MerchantTabadulLogin.TabadulPassword))
            .FirstOrDefaultAsync();
        if (merchantTabadulLogin is null)
        { return null; }

        await cachingService.SetAsync(cacheKey, merchantTabadulLogin);
        return merchantTabadulLogin.MerchantId;
    }
}
