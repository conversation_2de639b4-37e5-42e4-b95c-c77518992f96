using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Configurations;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Merchants.Shared;

namespace PaymentGateway.WebApi.Features.Merchants.Data;

public interface IMerchantConfigRepository : IRepository<MerchantConfig, MerchantConfigId>
{
    Task<MerchantConfigResponse?> FindByMerchantId(MerchantId merchantId, CancellationToken cancellationToken);
}

public class MerchantConfigRepository(PaymentDbContext context, AutoMapper.IMapper mapper, IMemoryCache cache,CachingConfiguration cachingConfiguration) : Repository<MerchantConfig, MerchantConfigId>(context), IMerchantConfigRepository
{
    private readonly PaymentDbContext _context = context;

    public async Task<MerchantConfigResponse?> FindByMerchantId(MerchantId merchantId, CancellationToken cancellationToken)
    {
        var cacheKey = $"{AppConstants.CachePrefixes.MerchantConfigPrefix}{merchantId}";
        if (cache.TryGetValue(cacheKey, out MerchantConfigResponse? cached)) return cached;
        
        var fresh = await _context.MerchantConfigs
            .Where(x => x.MerchantId == merchantId)
            .ProjectTo<MerchantConfigResponse>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);

        cache.Set(cacheKey, fresh, new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(cachingConfiguration.AbsoluteExpiration)
        });
        
        return fresh;
    }
}
