namespace PaymentGateway.WebApi.Common;

public class EntityBase;
public interface IEntity;
public interface IEntity<T> : IEntity;
public class EntityBase<T> : EntityBase, IEntity<T>
{
    public required T Id { get; set; }
}
public interface IGetListRequest
{
    public string? Query { get; set; }
    string? Sort { get; set; }
    int? Limit { get; set; }
    int? Offset { get; set; }
}
public abstract record GetListRequestBase : IGetListRequest
{
    public string? Query { get; set; }
    public string? Sort { get; set; } = "-id,name";
    public int? Limit { get; set; }
    public int? Offset { get; set; }
}