namespace PaymentGateway.WebApi.Common.Services;

public interface IRequestIdResolver
{
    string GetRequestId();
    void SetRequestId(string requestId);
}

public class RequestIdResolver(IHttpContextAccessor httpContextAccessor) : IRequestIdResolver
{
    public string GetRequestId()
    {
        return httpContextAccessor.HttpContext
            ?.Items[AppConstants.RequestIdHeaderName]
            ?.ToString() ?? throw new InvalidOperationException("Request ID is not set");
    }

    public void SetRequestId(string requestId)
    {
        if (httpContextAccessor.HttpContext == null) return;
        if (!httpContextAccessor.HttpContext.Items.TryAdd(AppConstants.RequestIdHeaderName, requestId))
            throw new InvalidOperationException("Request ID is already set");
    }
}
