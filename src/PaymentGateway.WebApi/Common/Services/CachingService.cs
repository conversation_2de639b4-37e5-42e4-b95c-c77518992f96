using Microsoft.Extensions.Caching.Memory;
using PaymentGateway.WebApi.Common.Configurations;

namespace PaymentGateway.WebApi.Common.Services;

public interface ICachingService
{
    Task<T?> GetOrCreateAsync<T>(string key, Func<Task<T>> createItem);
    Task<T?> GetAsync<T>(string key);
    Task SetAsync<T>(string key, T item);
    Task RemoveAsync(string key);
}

public class MemoryCachingService(CachingConfiguration cachingConfiguration, IMemoryCache memoryCache) : ICachingService
{
    public async Task<T?> GetOrCreateAsync<T>(string key, Func<Task<T>> createItem)
    {
        if (memoryCache.TryGetValue(key, out T? cachedItem)) return cachedItem;

        var item = await createItem();

        var cacheEntryOptions = GetMemoryCacheEntryOptions();
        memoryCache.Set(key, item, cacheEntryOptions);

        return item;
    }

    public Task<T?> GetAsync<T>(string key)
    {
        memoryCache.TryGetValue(key, out T? cachedItem);
        return Task.FromResult(cachedItem);
    }

    public Task SetAsync<T>(string key, T item)
    {
        var cacheEntryOptions = GetMemoryCacheEntryOptions();
        memoryCache.Set(key, item, cacheEntryOptions);
        return Task.CompletedTask;
    }

    public Task RemoveAsync(string key)
    {
        memoryCache.Remove(key);
        return Task.CompletedTask;
    }

    private MemoryCacheEntryOptions GetMemoryCacheEntryOptions() =>
        new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(cachingConfiguration.AbsoluteExpiration),
            SlidingExpiration = TimeSpan.FromSeconds(cachingConfiguration.ExpirationInSecond),
        };
}
