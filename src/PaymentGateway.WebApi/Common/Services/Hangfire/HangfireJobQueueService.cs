using System.Linq.Expressions;
using Hangfire;

namespace PaymentGateway.WebApi.Common.Services.Hangfire;

public interface IHangfireJobQueueService
{
    string Enqueue<T>(Expression<Action<T>> methodCall);
    string Schedule<T>(Expression<Action<T>> methodCall, TimeSpan delay);
    void AddRecurring<T>(string jobId, Expression<Action<T>> methodCall, string cronExpression);
    bool Delete(string jobId);
}

public class HangfireJobQueueService : IHangfireJobQueueService
{
    public string Enqueue<T>(Expression<Action<T>> methodCall)
    {
        return BackgroundJob.Enqueue(methodCall);
    }

    public string Schedule<T>(Expression<Action<T>> methodCall, TimeSpan delay) { return BackgroundJob.Schedule(methodCall, delay); }

    public void AddRecurring<T>(string jobId, Expression<Action<T>> methodCall, string cronExpression) { RecurringJob.AddOrUpdate(jobId, methodCall, cronExpression); }

    public bool Delete(string jobId) => BackgroundJob.Delete(jobId);
}
