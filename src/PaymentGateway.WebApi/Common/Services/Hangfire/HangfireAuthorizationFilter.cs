using System.Text;
using Hangfire.Dashboard;
using PaymentGateway.WebApi.Common.Configurations;

namespace PaymentGateway.WebApi.Common.Services.Hangfire
{
    public class HangfireAuthorizationFilter(ILogger<HangfireAuthorizationFilter> logger, HangfireConfiguration configuration) : IDashboardAuthorizationFilter
    {
        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();

            if (!httpContext.Request.Headers.TryGetValue("Authorization", out _))
            {
                logger.LogWarning("Authorization header missing.");
                httpContext.Response.Headers.WWWAuthenticate = "Basic realm=\"Hangfire Dashboard\"";
                return false;
            }

            var authHeader = httpContext.Request.Headers.Authorization.ToString();

            if (authHeader.StartsWith("Basic ", StringComparison.OrdinalIgnoreCase))
            {
                var token = authHeader["Basic ".Length..].Trim();
                try
                {
                    var credentialString = Encoding.UTF8.GetString(Convert.FromBase64String(token));
                    var credentials = credentialString.Split(':', 2);
                    if (credentials.Length == 2)
                    {
                        var username = credentials[0];
                        var password = credentials[1];
                        if (username == configuration.Username && password == configuration.Password)
                        {
                            logger.LogInformation("Authentication successful for user: {Username}", username);
                            return true;
                        }

                        httpContext.Response.Headers.WWWAuthenticate = "Basic realm=\"Hangfire Dashboard\"";
                        return false;
                    }
                }
                catch (FormatException)
                {
                    httpContext.Response.Headers.WWWAuthenticate = "Basic realm=\"Hangfire Dashboard\"";
                    return false;
                }
            }

            httpContext.Response.Headers.WWWAuthenticate = "Basic realm=\"Hangfire Dashboard\"";
            return false;
        }
    }
}
