namespace PaymentGateway.WebApi.Common.Services;

public interface IIpResolver
{
    string GetIp();
}

public class IpResolver : IIpResolver
{
    private readonly string _customHeader;
    private readonly bool _fallbackToRemoteIp;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public IpResolver(IHttpContextAccessor httpContextAccessor, IConfiguration configuration)
    {
        _httpContextAccessor = httpContextAccessor;

        var ipResolverConfig = configuration.GetSection("Application:IpResolver");
        _customHeader = ipResolverConfig.GetValue<string>("CustomHeader") ?? "X-Forwarded-For";
        _fallbackToRemoteIp = ipResolverConfig.GetValue<bool>("FallbackToRemoteIp");
    }

    public string GetIp()
    {
        var httpContext = _httpContextAccessor.HttpContext;

        if (httpContext == null) return string.Empty;

        if (httpContext.Request.Headers.TryGetValue(_customHeader, out var headerValues))
        {
            var ip = headerValues.FirstOrDefault()
                ?.Split(',')
                .FirstOrDefault()
                ?.Trim();
            if (!string.IsNullOrEmpty(ip)) return ip;
        }

        if (!_fallbackToRemoteIp) return string.Empty;
        var remoteIp = httpContext.Connection.RemoteIpAddress?.ToString();
        return remoteIp ?? string.Empty;
    }
}
