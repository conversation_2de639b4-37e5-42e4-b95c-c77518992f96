using PaymentGateway.Integrations.Services.Tabadul;

namespace PaymentGateway.WebApi.Common.Services;

public static class TabadulErrorParser
{
    public static Error Parse(int tabadulErrorCode, string? tabadulErrorMessage, string? tabadulInfo)
    {
        var responseErrorCode = (TabadulErrorCode)tabadulErrorCode;
        var errorCode = responseErrorCode == TabadulErrorCode.Success ? "Payment declined" : responseErrorCode.ToString();
        var statusCode = responseErrorCode 
            is TabadulErrorCode.Success 
            or TabadulErrorCode.ExpectedOrderIdOrOrderNumber 
            or TabadulErrorCode.OrderDeclinedCredentialsError
            or TabadulErrorCode.AccessDeniedOrUserMustChangePasswordOrOrderIdEmpty
            ? StatusCode.BadRequest
            : StatusCode.BadGateway;
        return new Error(ErrorNumbers.TabadulError, errorCode, tabadulInfo ?? tabadulErrorMessage, (int)statusCode);
    }
}
