namespace PaymentGateway.WebApi.Common;

public abstract class AppConstants
{
    public const string BusinessErrorTitle = "Business error";
    public const string ValidationErrorTitle = "Validation error";
    public const string GenericErrorTitle = "Generic Error";
    public const string RequestIdHeaderName = "X-Request-Id";
    public const string DefaultCustomerId = "UNKNOWN";

    public abstract class Regex
    {
        public const string EmailPattern = @"^[A-Za-z0-9._%+\-]*[A-Za-z][A-Za-z0-9._%+\-]*@[A-Za-z0-9\-]+\.[A-Za-z]{2,}$";
        public const string UrlPattern = @"^(https?:\/\/)(?:(?:[a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,}|\d{1,3}(?:\.\d{1,3}){3})(?::\d+)?(?:\/\S*)?$";
        
        public const string PhonePattern = @"^07[5789]\d{8}$";
        
    }
    public abstract class CachePrefixes
    {
        public const string MerchantPrefix = "Merchant_";
        public const string MerchantConfigPrefix = "MerchantConfig_";
        public const string MerchantCredentialPrefix = "MerchantCredential_";
        public const string MerchantTabadulUsernamePrefix = "MerchantTabulUsername_";
    }

    public abstract class ConnectionStrings
    {
        public const string Postgres = "Postgres";
        public const string ClickhouseOrdersDb = "Clickhouse_OrdersDb";
        public const string HangfirePostgres = "Hangfire_Postgres";
    }

    public abstract class CustomClaims
    {
        public const string MerchantId = "MerchantId";
        public const string MerchantLoginId = "MerchantLoginId";
        public const string MerchantTabadulLoginId = "MerchantTabadulLoginId";
        public const string ApiKey = "ApiKey";
    }

    public abstract class SecuritySchemes
    {
        public const string ApiKeySchemeName = "APIKeyScheme";
        public const string ApiKeyScheme = "X-Api-Key";
        public const string BasicSchemeName = "BasicScheme";
        public const string BasicScheme = "basic";
    }
    public abstract class Policies
    {
        public const string ApiKeyAndBasic = "ApiKeyAndBasic";
        public const string ApiKeyOnly = "ApiKeyOnly";
        public const string BasicOnly = "BasicOnly";
    }

    public abstract class OrderEventDescriptions
    {
        public const string OrderCreated = "Order created successfully";
        public const string OrderPaymentSuccessful = "Order payed successful";
        public const string OrderConfirmSuccessful = "Order confirm successful";
        public const string OrderConfirmFailed = "Order confirm failed";
        public const string OrderPaymentFailed = "Order payed failed";
        public const string MerchantNotifiedSuccessfully = "Merchant notified successfully";
        public const string MerchantMaxNotificationTriesReached = "Merchant max notification tries reached";
        public const string MerchantNotificationFailed = "Merchant notification failed";
        public const string ConnectionToMerchantFailed = "Connection to merchant failed";
        public const string OrderCanceled = "Order canceled by customer";
    }

    public abstract class Tabdul
    {
        private const string BaseUrl = "epg/rest";
        public const string OrderRegister = $"{BaseUrl}/register.do";
        public const string OrderStatus = $"{BaseUrl}/getOrderStatus.do";
    }

}
