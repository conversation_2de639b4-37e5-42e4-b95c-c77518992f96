namespace PaymentGateway.WebApi.Common.Configurations;

public class ProvisionerConfiguration
{
    public int ProvisionBeginsInSeconds { get; set; }
    public int RetryCount { get; set; }
    public int[] RetryIntervalsInSeconds { get; set; } = [];

    /// <summary>
    /// Feature flag to control whether orders should be sent to the ProvisionService.
    /// When false, orders will be registered but not sent to provisioning.
    /// Default: true (enabled)
    /// </summary>
    public bool EnableProvisionService { get; set; } = true;
}
