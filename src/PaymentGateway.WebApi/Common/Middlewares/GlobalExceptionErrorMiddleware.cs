using System.Text.Json;
using PaymentGateway.WebApi.Common.Services;

namespace PaymentGateway.WebApi.Common.Middlewares;

public class GlobalExceptionHandlerMiddleware(ILogger<GlobalExceptionHandlerMiddleware> logger, RequestDelegate next)
{
    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            await next(httpContext);
        }

        //todo: add custom exceptions handling cases here
        catch (Exception e)
        {
            var requestIdResolver = httpContext.RequestServices.GetRequiredService<IRequestIdResolver>();
            var requestId = requestIdResolver.GetRequestId();
            //todo: log the error properly using some tool like serilog or elasticsearch
            logger.LogError(e, "requestId: {requestId}, exceptionDate: {date}, errorMessage: {Message}", requestId, DateTimeOffset.UtcNow, e.Message);
            if (!httpContext.Response.HasStarted)
            {
                var problemDetails = new GenericError(AppConstants.GenericErrorTitle, "Something went wrong", AppConstants.GenericErrorTitle, requestId);
                httpContext.Response.ContentType = "application/json";
                httpContext.Response.StatusCode = 400;
                await httpContext.Response.WriteAsync(JsonSerializer.Serialize(problemDetails));
            }
        }
    }
}

