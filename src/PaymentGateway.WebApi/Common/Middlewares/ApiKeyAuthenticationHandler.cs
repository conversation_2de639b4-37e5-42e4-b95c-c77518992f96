using PaymentGateway.WebApi.Auth.Configurations;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Features.Merchants.Data;

namespace PaymentGateway.WebApi.Common.Middlewares;

using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

public class ApiKeyAuthenticationHandler(
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory logger,
    UrlEncoder encoder,
    AuthConfiguration authConfig,
    IMerchantLoginRepository merchantLoginRepo,
    ICachingService cache) : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (!Request.Headers.TryGetValue(authConfig.ApiKeyHeaderName, out var rawApiKey) || string.IsNullOrWhiteSpace(rawApiKey))
        {
            return AuthenticateResult.Fail("No API Key");
        }

        var apiKey = rawApiKey.ToString();

        var cacheKey = $"{AppConstants.CachePrefixes.MerchantPrefix}{apiKey}";
        var merchant = await cache.GetAsync<MerchantLogin?>(cacheKey);
        if (merchant is null)
        {
            merchant = await merchantLoginRepo.FindByApiKey(apiKey);
            if (merchant is null)
            {
                return AuthenticateResult.Fail("Invalid API Key");
            }
            await cache.SetAsync(cacheKey, merchant);
        }

        var claims = GetClaim(merchant);
        var identity = new ClaimsIdentity(claims, Scheme.Name);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, Scheme.Name);

        return AuthenticateResult.Success(ticket);
    }
    
    private static List<Claim> GetClaim(MerchantLogin merchant) =>
    [
        new(AppConstants.CustomClaims.MerchantId, merchant.MerchantId.ToString()), new(AppConstants.CustomClaims.MerchantLoginId, merchant.Id.ToString()),
        new(AppConstants.CustomClaims.MerchantTabadulLoginId, merchant.MerchantTabadulLoginId.ToString()), new(AppConstants.CustomClaims.ApiKey, merchant.ApiKey),
    ];
}