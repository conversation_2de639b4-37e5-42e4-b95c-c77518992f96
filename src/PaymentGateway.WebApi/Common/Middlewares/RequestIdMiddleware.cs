using PaymentGateway.WebApi.Common.Services;

namespace PaymentGateway.WebApi.Common.Middlewares;

public class RequestIdMiddleware(RequestDelegate next, ILogger<RequestIdMiddleware> logger)
{

    public async Task InvokeAsync(HttpContext context)
    {
        var requestIdResolver = context.RequestServices.GetRequiredService<IRequestIdResolver>();
        var requestId = Ulid.NewUlid().ToString();
        requestIdResolver.SetRequestId(requestId);
        context.Items[AppConstants.RequestIdHeaderName] = requestId;
        context.Response.OnStarting(() =>
        {
            context.Response.Headers[AppConstants.RequestIdHeaderName] = requestId;
            return Task.CompletedTask;
        });
        
        logger.LogInformation("Handling request with {RequestId}", requestId);
        await next(context);
        logger.LogInformation("Finished handling request with {RequestId}", requestId);
    }
}
