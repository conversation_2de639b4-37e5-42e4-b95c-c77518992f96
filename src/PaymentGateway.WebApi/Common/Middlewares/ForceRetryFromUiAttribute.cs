namespace PaymentGateway.WebApi.Common.Middlewares;

using Hangfire.Common;
using Hangfire.States;
using Hangfire.Storage;

public class ForceRetryFromUiAttribute : JobFilterAttribute, IApplyStateFilter
{
    public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
    {
        if (context.NewState is not EnqueuedState enqueuedState) return;
        // Dashboard "Enqueue"/"Retry" sets Reason = "Triggered by user" or similar
        var isManual = enqueuedState.Reason?.Contains("Triggered via Dashboard UI", StringComparison.OrdinalIgnoreCase) == true;

        context.Connection.SetJobParameter(context.JobId, "ForceRetry", isManual ? "true" : "false");
    }

    public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction) { }
}