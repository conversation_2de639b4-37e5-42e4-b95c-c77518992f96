using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using PaymentGateway.WebApi.Features.Merchants.Data;

namespace PaymentGateway.WebApi.Common.Middlewares;

public class BasicAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options, ILoggerFactory logger, IMerchantTabadulLoginRepository merchantTabadulLoginRepository, UrlEncoder encoder)
    : AuthenticationHandler<AuthenticationSchemeOptions>(options, logger, encoder)
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (Request.Headers.Authorization.Count == 0) return AuthenticateResult.Fail("Missing Authorization Header");
        try
        {
            var authHeader = AuthenticationHeaderValue.Parse(Request.Headers.Authorization!);
            if (!AppConstants.SecuritySchemes.BasicScheme.Equals(authHeader.Scheme, StringComparison.OrdinalIgnoreCase)) return AuthenticateResult.Fail("Invalid Authorization Scheme");

            var credentialBytes = Convert.FromBase64String(authHeader.Parameter ?? "");
            var credentials = Encoding.UTF8
                .GetString(credentialBytes)
                .Split(':', 2);
            if (credentials.Length != 2) return AuthenticateResult.Fail("Invalid Basic Authentication Header");

            var username = credentials[0];
            var password = credentials[1];

            var merchantId = await TryGetMerchantId(username, password);
            if (merchantId is null) return AuthenticateResult.Fail("Invalid Username or Password");
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, username),
                new Claim(ClaimTypes.Name, username),
                new Claim(AppConstants.CustomClaims.MerchantId, merchantId)
            };

            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, Scheme.Name);

            return await Task.FromResult(AuthenticateResult.Success(ticket));
        }
        catch { return AuthenticateResult.Fail("Invalid Authorization Header"); }
    }

    private async Task<string?> TryGetMerchantId(string username, string password)
    {
        var merchantId = await merchantTabadulLoginRepository.TryGetMerchantIdAsync(username, password);
        return merchantId;
    }
}
