namespace PaymentGateway.WebApi.Common;

public enum StatusCode
{
    Ok = 200,
    Created = 201,
    Accepted = 202,
    NoContent = 204,
    BadRequest = 400,
    Unauthorized = 401,
    Forbidden = 403,
    NotFound = 404,
    Conflict = 409,
    UnprocessableEntity = 422,
    InternalServerError = 500,
    BadGateway = 502,
    ServiceUnavailable = 503,
}

public record Error(int Number,string Code, string? Message = null, int StatusCode = (int)StatusCode.BadRequest)
{
    public static readonly Error None = new(ErrorNumbers.None,string.Empty);

    public static implicit operator Result(Error error) => Result.Failure(error);

    public IResult Problem() =>
        Results.Problem(title: Code, 
            detail: Message, statusCode: StatusCode, type: Number.ToString());
    
    public static Error Create(int number,string code, string message) { return new Error(number,code, message); }
    public static Error Create(int number,string code, string message, int statusCode) { return new Error(number,code, message, (int)statusCode); }

}

public sealed record Error<T>(int Number,string Code, string? Message = null, int StatusCode = (int)StatusCode.BadRequest) : Error(Number,Code, Message, StatusCode) where T : notnull
{
    public static implicit operator Result<T>(Error<T> error) => Result<T>.Failure(error);
}


public class Result
{
    protected Result(StatusCode statusCode, Error error)
    {
        StatusCode = statusCode;
        if ((IsSuccess && error != Error.None) || (!IsSuccess && error == Error.None)) throw new ArgumentException("Success result can't have error", nameof(error));
        Error = error;
    }

    public StatusCode StatusCode { get; }
    public bool IsSuccess => (int)StatusCode < 300;
    public bool IsFailure => !IsSuccess;
    public Error Error { get; }

    public static Result Success(StatusCode statusCode = StatusCode.Ok) => new(statusCode, Error.None);

    public static Result Failure(Error error, StatusCode statusCode = StatusCode.BadRequest) => new(statusCode, error);
}

public sealed class Result<T> : Result where T : notnull
{
    private Result(StatusCode statusCode, Error error, T data = default!) : base(statusCode, error) => Data = data;

    public T Data { get; }

    public static Result<T> Success(T data = default!, StatusCode statusCode = StatusCode.Ok) => new(statusCode, Error.None, data);

    public new static Result<T> Failure(Error error, StatusCode statusCode = StatusCode.BadRequest) => new(statusCode, error);

    public override string ToString() => IsSuccess ? $"Success: {Data}" : $"Failure: {Error.Message}";
    
}

public record ValidationProblemDetails(int ErrorNumber,string Title, int Status, ValidationError[] Errors);

public record ValidationError(string PropertyName, string Code, string? Message);

public record GenericError(string Code, string Message, string Title, string ErrorId);

