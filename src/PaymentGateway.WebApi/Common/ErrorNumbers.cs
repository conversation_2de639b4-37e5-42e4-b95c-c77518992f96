namespace PaymentGateway.WebApi.Common;
public abstract class ErrorNumbers
{
    public const int None = 0;
    public const int ValidationError = 1;
    public const int TabadulError =2 ;
    public abstract class AuthErrors
    {
        public const int ApiKeyMissing = 11;
        public const int ApiKeyInvalid = 12;
    }
    public abstract class ClickhouseContextErrors
    {
        public const int QueryMustNotBeEmpty = 13;
        public const int NotFound = 14;
        public const int NoRowEffected = 15;
    }
    public abstract class OrderErrors
    {
        public const int OrderAmountMustBeGreaterThanZero = 16;
        public const int OrderCurrencyMustBeValid = 17;
        public const int OrderPhoneMustBeStartWith07 = 18;
        public const int OrderEmailMustBeValid = 19;
        public const int OrderIdIsNotValid = 20;
        public const int MerchantIdMissing = 21;
        public const int OpenedOrderDetected = 22;
        public const int OrderNotFound = 23;
        public const int CvvShouldBe3Digits = 24;
        public const int OrderIsAlreadyPaid = 25;
        public const int OrderStatusRedirectUrlMustBeValid = 34;
        public const int OrderAlreadyClosed = 35;
        public const int OrderStatusTokenIsInvalid = 36;
        public const int MerchantConfigIsMissing = 37;
        public const int InvalidStatusToken = 38;
        public const int CustomerIdIsNotValid = 39;
        public const int OrderCannotCanceled = 40;
        public const int ExternalIdMustProvidedWhenOrderIdIsNotProvided = 41;
        public const int EitherOrderIdOrExternalIdMustBeProvided = 42;
    }
    
    // public abstract class CardErrors
    // {
    //     public const int CardNumberBe16Digits = 26;
    //     public const int YearOfExpiryShouldBeBetweenCurrentYearAndNext50 = 27;
    //     public const int MonthOfExpiryShouldBeBetween1And12 = 28;
    //     public const int CardHolderNameShouldValidName = 29;
    //     public const int YearOfExpiryShouldBe4Digits = 30;
    //     public const int MonthOfExpiryShouldBe2Digits = 31;
    //     public const int CardHolderNameShouldBeLessThan27Characters = 32;
    //     
    // }
    public abstract class TestErrors
    {
        public const int TestIdMustBeValid = 33;
    }
}
