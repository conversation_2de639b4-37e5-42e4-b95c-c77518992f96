using Hangfire;
using Hangfire.Dashboard;
using Hangfire.PostgreSql;
using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Auth.Configurations;
using PaymentGateway.WebApi.Common.Configurations;
using PaymentGateway.WebApi.Common.Services.Hangfire;
using PaymentGateway.WebApi.Data;
using Serilog;

namespace PaymentGateway.WebApi.Common.Extensions;

public static class WebApplicationBuilderExtension
{
    public static WebApplicationBuilder AddCommon(this WebApplicationBuilder builder)
    {
        builder.AddLocalConfiguration()
            .AddSerilog()
            .AddDbContext()
            .AddClickhouseConfigurations()
            .AddCacheConfigurations()
            .AddHeaderConfiguration()
            .AddHangfireConfigurations()
            .AddAutoMapper()
            .Services
            .AddCommonServices();
        return builder;
    }

    private static WebApplicationBuilder AddAutoMapper(this WebApplicationBuilder builder)
    {
        var key = builder.Configuration.GetValue<string>("AutoMapper:LicenseKey");
        builder.Services.AddAutoMapper(cfg => { cfg.LicenseKey = key; }, typeof(Program));
        return builder;
    }

    private static WebApplicationBuilder AddSerilog(this WebApplicationBuilder builder)
    {
        Log.Logger = new LoggerConfiguration().ReadFrom
            .Configuration(builder.Configuration)
            .Enrich
            .WithCorrelationId()
            .CreateLogger();
        builder.Host.UseSerilog(Log.Logger);

        return builder;
    }

    private static WebApplicationBuilder AddHangfireConfigurations(this WebApplicationBuilder builder)
    {
        
        builder.Services.Configure<ProvisionerConfiguration>(builder.Configuration.GetSection("Hangfire:Provisioner"));
        builder.Services.AddSingleton<ProvisionerConfiguration>();
        var provisionerConfig = builder.Configuration.GetConfiguration<ProvisionerConfiguration>("Hangfire:Provisioner");
        builder.Services.AddHangfire(config =>
        {
            config.UsePostgreSqlStorage(c => c.UseNpgsqlConnection(builder.Configuration.GetConnectionString(AppConstants.ConnectionStrings.HangfirePostgres)));
            config.UseFilter(new AutomaticRetryAttribute
            {
                Attempts = provisionerConfig.RetryCount,
                DelaysInSeconds = provisionerConfig.RetryIntervalsInSeconds,
                LogEvents = true,
                OnAttemptsExceeded = AttemptsExceededAction.Fail
                
            });
        });
        builder.Services.AddHangfireServer();
        builder.Services.AddScoped<IHangfireJobQueueService, HangfireJobQueueService>();
        builder.Services.AddSingleton<IDashboardAuthorizationFilter, HangfireAuthorizationFilter>();

        var config = builder.Configuration.GetConfiguration<HangfireConfiguration>("Hangfire:Dashboard");
        builder.Services.AddSingleton(config);
        return builder;
    }

    private static WebApplicationBuilder AddCacheConfigurations(this WebApplicationBuilder builder)
    {
        var config = builder.Configuration.GetConfiguration<CachingConfiguration>("Cache");
        builder.Services.AddSingleton(config);

        return builder;
    }

    private static WebApplicationBuilder AddHeaderConfiguration(this WebApplicationBuilder builder)
    {
        var config = builder.Configuration.GetConfiguration<AuthConfiguration>("Auth");
        builder.Services.AddSingleton(config);
        return builder;
    }

    private static WebApplicationBuilder AddLocalConfiguration(this WebApplicationBuilder builder)
    {
        if (builder.Environment.IsDevelopment()) builder.Configuration.AddJsonFile("appsettings.Local.json", true, true);

        return builder;
    }

    private static WebApplicationBuilder AddDbContext(this WebApplicationBuilder builder)
    {
        builder.Services.AddDbContext<PaymentDbContext>(o => o
            .UseNpgsql(builder.Configuration.GetConnectionString(AppConstants.ConnectionStrings.Postgres), n => n.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery))
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .UseSnakeCaseNamingConvention());

        return builder;
    }

    private static WebApplicationBuilder AddClickhouseConfigurations(this WebApplicationBuilder builder)
    {
        builder.Services.AddSingleton<ClickhouseConfiguration>(provider => new ClickhouseConfiguration
        {
            OrdersConnectionString = builder.Configuration.GetConnectionString(AppConstants.ConnectionStrings.ClickhouseOrdersDb) ?? throw new ArgumentNullException("ClickHouseConnection"),
        });
        builder.Services.AddScoped(typeof(IClickhouseContext<>), typeof(ClickhouseContext<>));

        return builder;
    }
}
