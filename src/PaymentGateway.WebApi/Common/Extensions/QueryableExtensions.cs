using System.Linq.Expressions;

namespace PaymentGateway.WebApi.Common.Extensions
{
    public static class QueryableExtensions
    {
        public static IQueryable<T> WhereIf<T>(this IQueryable<T> query, bool condition, Expression<Func<T, bool>> predicate) =>
            condition ? query.Where(predicate) : query;

        public static IQueryable<TEntity> ApplySorting<TEntity>(this IQueryable<TEntity> query, IGetListRequest getListRequest) where TEntity : class
        {
            return ApplyOrderBy(query, getListRequest);
        }

        private static void ValidatePropertiesExist<TEntity>(string[] propertyNames)
        {
            var entityType = typeof(TEntity);
            foreach (var propertyName in propertyNames)
            {
                if (string.IsNullOrWhiteSpace(propertyName)) continue;

                var cleanPropertyName = propertyName.TrimStart('-');
                var propertyInfo = entityType.GetProperty(cleanPropertyName, System.Reflection.BindingFlags.IgnoreCase | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                if (propertyInfo == null) throw new ArgumentException($"No property '{cleanPropertyName}' exists on type '{entityType.FullName}'.");
            }
        }

        private static IQueryable<TEntity> ApplyOrderBy<TEntity>(IQueryable<TEntity> query, IGetListRequest getListRequest) where TEntity : class
        {
            var defaultSortProperty = typeof(TEntity).GetProperty("Id", System.Reflection.BindingFlags.IgnoreCase | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance) != null ? "Id" : null;
            if (defaultSortProperty == null && getListRequest.Sort == null) return query;

            var sortProperties = (getListRequest.Sort ?? defaultSortProperty)?.Split(',')
                .Select(p => p.Trim())
                .ToArray();

            if (sortProperties == null || sortProperties.Length == 0) return query;

            ValidatePropertiesExist<TEntity>(sortProperties);

            var isFirstSortProperty = true;
            foreach (var sortProperty in sortProperties)
            {
                var sortAscending = !sortProperty.StartsWith("-");
                var cleanSortProperty = sortProperty.TrimStart('-');

                query = ApplyOrderByHelper(query, cleanSortProperty, sortAscending, isFirstSortProperty);
                isFirstSortProperty = false;
            }

            return query;
        }

        private static IQueryable<TEntity> ApplyOrderByHelper<TEntity>(IQueryable<TEntity> query, string sortProperty, bool sortAscending, bool isFirstSortProperty) where TEntity : class
        {
            var parameter = Expression.Parameter(typeof(TEntity), "x");
            var property = Expression.Property(parameter, sortProperty);
            var propertyType = property.Type;

            var delegateType = typeof(Func<,>).MakeGenericType(typeof(TEntity), propertyType);
            var lambda = Expression.Lambda(delegateType, property, parameter);

            string method;
            if (isFirstSortProperty)
                method = sortAscending ? "OrderBy" : "OrderByDescending";
            else
                method = sortAscending ? "ThenBy" : "ThenByDescending";

            var genericMethod = typeof(Queryable).GetMethods()
                .Single(m => m.Name == method && m.IsGenericMethodDefinition && m.GetGenericArguments().Length == 2 && m.GetParameters().Length == 2)
                .MakeGenericMethod(typeof(TEntity), propertyType);

            return (IQueryable<TEntity>)genericMethod.Invoke(null, new object[] { query, lambda })!;
        }

        public static IQueryable<TEntityBase> ApplyPagination<TEntityBase>(this IQueryable<TEntityBase> query, IGetListRequest getListRequest)
        {
            var limit = getListRequest.Limit ?? 25;
            var offset = getListRequest.Offset ?? 0;
            return query.Skip(offset)
                .Take(limit);
        }
    }
}
