using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Data;

namespace PaymentGateway.WebApi.Common.Extensions;

public static class WebApplicationExtensions
{
    public static async Task MigrateAsync(this WebApplication app)
    {
        await using var scope = app.Services.CreateAsyncScope();
        var context = scope.ServiceProvider.GetRequiredService<PaymentDbContext>();
        await context.Database.MigrateAsync();
        await context.Database.EnsureCreatedAsync();
    }
}
