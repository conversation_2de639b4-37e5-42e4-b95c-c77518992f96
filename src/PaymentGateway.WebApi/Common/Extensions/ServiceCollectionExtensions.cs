using System.Text.Json;
using System.Text.Json.Serialization;
using Cysharp.Serialization.Json;
using FastEndpoints;
using FastEndpoints.Swagger;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using NSwag;
using PaymentGateway.WebApi.Common.Services;
using PaymentGateway.WebApi.Data;
using PaymentGateway.WebApi.Features.Payments.Services;

namespace PaymentGateway.WebApi.Common.Extensions;

public static class ServiceCollectionExtensions
{
    public static void AddCommonServices(this IServiceCollection services)
    {
        services.AddSwagger()
            .AddHttpJsonOptions()
            .RegisterServices()
            .AddFastEndpoints()
            .AddEndpointsApiExplorer()
            .AddMemoryCache();
    }

    private static IServiceCollection AddSwagger(this IServiceCollection services)
    {
        services.SwaggerDocument(o =>
        {
            o.EnableJWTBearerAuth = false;
            o.DocumentSettings = s =>
            {
                s.AddAuth(AppConstants.SecuritySchemes.BasicSchemeName, new OpenApiSecurityScheme
                {
                    Type = NSwag.OpenApiSecuritySchemeType.Http,
                    Scheme = AppConstants.SecuritySchemes.BasicScheme,
                    Description = "Input your username and password to access this API"
                });

                s.AddAuth(AppConstants.SecuritySchemes.ApiKeySchemeName, new OpenApiSecurityScheme
                {
                    Type = OpenApiSecuritySchemeType.ApiKey,
                    Name = AppConstants.SecuritySchemes.ApiKeyScheme,
                    In = OpenApiSecurityApiKeyLocation.Header,
                    Description = "API Key needed to access the endpoints.\r\n\r\n" + "Enter your API key in the text input below.\r\n\r\n" + "Example: \"12345abcdef\""
                });
            };
        });
        return services;
    }
    private static IServiceCollection RegisterServices(this IServiceCollection services)

    {
        services.AddScoped<IRequestIdResolver, RequestIdResolver>();
        services.AddScoped<IClickHouseConnectionFactory, ClickHouseConnectionFactory>();
        services.AddScoped<ICachingService, MemoryCachingService>();
        services.AddScoped<IMerchantNotifier, MerchantNotifier>();
        services.AddScoped<IIpResolver, IpResolver>();
        return services;
    }

    private static IServiceCollection AddHttpJsonOptions(this IServiceCollection services)
    {
        JsonConvert.DefaultSettings = () => new JsonSerializerSettings
        {
            Converters = { new StringEnumConverter() },
        };
        services.ConfigureHttpJsonOptions(o =>
        {
            o.SerializerOptions.Converters.Add(new UlidJsonConverter());
            o.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            o.SerializerOptions.Converters.Add(new JsonStringEnumConverter(JsonNamingPolicy.CamelCase));
        });

        return services;
    }

}
