namespace PaymentGateway.WebApi.Common;

public class AppRoute
{
    public const string IdKey = "{id}";
    public const string GuidIdKey = "{id:guid}";
    public const string IdValue = "id";
    private const string Path= "/api";
    private const string TabadulBasePath = $"{Path}/tabadul";    

    public class Orders
    {
        private const string Path = "orders";
        public const string FullPath = $"{TabadulBasePath}/{Path}";
        public const string IdPath = $"{FullPath}/{GuidIdKey}";
        public const string Register = $"{FullPath}/register";
        public const string Pay = $"{FullPath}/pay";
        public const string UiRegister = $"{TabadulBasePath}/ui-{Path}/register";
        public const string Confirm = $"{TabadulBasePath}/ui-{Path}/confirm-payment";
        public const string Cancel = $"{FullPath}/cancel/{IdKey}";
    }

    public class Tests
    {
        private const string Path = "tests";
        public const string FullPath = "tests";
    }
}
