using PaymentGateway.WebApi.Common;

namespace PaymentGateway.WebApi.Data;

public abstract class ClickhouseContextErrors
{
    public static Error QueryMustNotBeEmpty => new(ErrorNumbers.ClickhouseContextErrors.QueryMustNotBeEmpty,nameof(QueryMustNotBeEmpty), "Query must not be empty.");
    public static Error NotFound  => new(ErrorNumbers.ClickhouseContextErrors.NotFound,nameof(NotFound), "Not found.");
    public static Error NoRowEffected  => new(ErrorNumbers.ClickhouseContextErrors.NoRowEffected,nameof(NoRowEffected), "No row effected.");
}
