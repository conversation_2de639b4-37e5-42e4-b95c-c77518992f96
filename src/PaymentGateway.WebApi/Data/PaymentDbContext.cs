using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;
// using PaymentGateway.WebApi.Features.Payments.Cards.Data;

namespace PaymentGateway.WebApi.Data;

public class PaymentDbContext(DbContextOptions<PaymentDbContext> options) : DbContext(options)
{
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(PaymentDbContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<Merchant> Merchants => Set<Merchant>();
    public DbSet<MerchantConfig> MerchantConfigs => Set<MerchantConfig>();
    public DbSet<MerchantLogin> MerchantLogins => Set<MerchantLogin>();
    public DbSet<MerchantTabadulLogin> MerchantTabadulLogins => Set<MerchantTabadulLogin>();
    // public DbSet<Card> Cards => Set<Card>();
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderEvent> OrderEvents => Set<OrderEvent>();
}
