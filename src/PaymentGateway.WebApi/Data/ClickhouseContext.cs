using Dapper;
using PaymentGateway.WebApi.Common;

namespace PaymentGateway.WebApi.Data;

public sealed class ClickhouseContext<TModel>( ILogger<ClickhouseContext<TModel>> logger, IClickHouseConnectionFactory connectionFactory) : IClickhouseContext<TModel> where TModel : class
{
    public async Task<Result<IEnumerable<TModel>>> GetAsync(string query, object? parameters = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query)) return Result<IEnumerable<TModel>>.Failure(ClickhouseContextErrors.QueryMustNotBeEmpty);
        await using var connection = connectionFactory.CreateConnection();
        await connection.OpenAsync(cancellationToken);
        logger.LogInformation("Executing query: {Query}", query);
        var result = await connection.QueryAsync<TModel>(query, parameters);
        return Result<IEnumerable<TModel>>.Success(result);
    }

    public async Task<Result<TModel>> FindAsync(string query, object? parameters = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query)) return Result<TModel>.Failure(ClickhouseContextErrors.QueryMustNotBeEmpty);
        await using var connection = connectionFactory.CreateConnection();
        await connection.OpenAsync(cancellationToken);
        logger.LogInformation("Executing query: {Query}", query);
        var result = await connection.QueryFirstOrDefaultAsync<TModel>(query, parameters);
        return result == null ? Result<TModel>.Failure(ClickhouseContextErrors.NotFound) : Result<TModel>.Success(result);
    }

    public async Task<Result> ExecuteAsync(string query, object? parameters = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(query)) return Result.Failure(ClickhouseContextErrors.QueryMustNotBeEmpty);
        await using var connection = connectionFactory.CreateConnection();
        await connection.OpenAsync(cancellationToken);
        logger.LogInformation("Executing command: {Query}", query);
        var effectedRow =await connection.ExecuteAsync(query, parameters);

        return effectedRow > 0 ? Result.Success() : Result.Failure(ClickhouseContextErrors.NoRowEffected);
    }
}
