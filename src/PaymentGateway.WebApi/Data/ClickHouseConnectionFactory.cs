using Octonica.ClickHouseClient;
using PaymentGateway.WebApi.Common.Configurations;

namespace PaymentGateway.WebApi.Data;

public class ClickHouseConnectionFactory : IClickHouseConnectionFactory
{
    private readonly string? _connectionString;

    public ClickHouseConnectionFactory(ClickhouseConfiguration configuration)
    {
        if (string.IsNullOrWhiteSpace(configuration.OrdersConnectionString)) throw new ArgumentException("OrdersConnectionString must be provided in the configuration.", nameof(configuration));

        _connectionString = configuration.OrdersConnectionString;
    }

    public ClickHouseConnection CreateConnection() => new(_connectionString!);
}
