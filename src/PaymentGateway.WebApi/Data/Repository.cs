using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using PaymentGateway.WebApi.Common;

namespace PaymentGateway.WebApi.Data;

public interface IRepository<TEntity, in TId> where TEntity : class, IEntity<TId>
{
    Task<TEntity?> FindAsync(TId id);
    Task<TEntity?> FindAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken);
    Task<TEntity> AddAsync(TEntity entity, bool save = false, CancellationToken cancellationToken = default);
    Task<TEntity> UpdateAsync(TEntity entity, bool save = false, CancellationToken cancellationToken = default);
    IQueryable<TEntity> GetQueryable();
    Task SaveChangesAsync(CancellationToken cancellationToken);
}

public class Repository<TEntity, TId>(DbContext context) : IRepository<TEntity, TId> where TEntity : class, IEntity<TId>
{
    public async Task<TEntity?> FindAsync(TId id)
    {
        return await context.Set<TEntity>()
            .FindAsync(id);
    }

    public async Task<TEntity?> FindAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken)
    {
        return await context.Set<TEntity>()
            .FirstOrDefaultAsync(predicate, cancellationToken);
    }

    public virtual async Task<TEntity> AddAsync(TEntity entity, bool save = false, CancellationToken cancellationToken = default)
    {
        var entry = await context.Set<TEntity>()
            .AddAsync(entity, cancellationToken);
        if (save) await SaveChangesAsync(cancellationToken);

        return entry.Entity;
    }

    public virtual async Task<TEntity> UpdateAsync(TEntity entity, bool save = false, CancellationToken cancellationToken = default)
    {
        context.Set<TEntity>()
            .Update(entity);
        if (save) await SaveChangesAsync(cancellationToken);
        return entity;
    }

    public IQueryable<TEntity> GetQueryable() => context.Set<TEntity>()
        .AsQueryable();

    public virtual async Task SaveChangesAsync(CancellationToken cancellationToken) => await context.SaveChangesAsync(cancellationToken);
}
