using PaymentGateway.WebApi.Common;

namespace PaymentGateway.WebApi.Data;

public interface IClickhouseContext<TModel> where TModel : class
{
    Task<Result<IEnumerable<TModel>>> GetAsync(string query, object? parameters = null, CancellationToken cancellationToken = default);
    Task<Result<TModel>> FindAsync(string query, object? parameters = null, CancellationToken cancellationToken = default);
    Task<Result> ExecuteAsync(string query, object? parameters = null, CancellationToken cancellationToken = default);
}
