using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddNewPropertiesToOrders : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "job_id",
                table: "orders",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "secure_failure_token",
                table: "orders",
                type: "character varying(26)",
                maxLength: 26,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "secure_success_token",
                table: "orders",
                type: "character varying(26)",
                maxLength: 26,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "ix_orders_job_id",
                table: "orders",
                column: "job_id");

            migrationBuilder.CreateIndex(
                name: "ix_orders_secure_failure_token",
                table: "orders",
                column: "secure_failure_token");

            migrationBuilder.CreateIndex(
                name: "ix_orders_secure_success_token",
                table: "orders",
                column: "secure_success_token");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_orders_job_id",
                table: "orders");

            migrationBuilder.DropIndex(
                name: "ix_orders_secure_failure_token",
                table: "orders");

            migrationBuilder.DropIndex(
                name: "ix_orders_secure_success_token",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "job_id",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "secure_failure_token",
                table: "orders");

            migrationBuilder.DropColumn(
                name: "secure_success_token",
                table: "orders");
        }
    }
}
