using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCardIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_cards_card_number",
                table: "cards");

            migrationBuilder.CreateIndex(
                name: "ix_cards_customer_id_card_number",
                table: "cards",
                columns: new[] { "customer_id", "card_number" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_cards_customer_id_card_number",
                table: "cards");

            migrationBuilder.CreateIndex(
                name: "ix_cards_card_number",
                table: "cards",
                column: "card_number",
                unique: true);
        }
    }
}
