using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCards : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_merchant_configs_merchant_tabadul_logins_merchant_tabadul_l",
                table: "merchant_configs");

            migrationBuilder.DropIndex(
                name: "ix_merchant_configs_merchant_tabadul_login_id",
                table: "merchant_configs");

            migrationBuilder.DropColumn(
                name: "merchant_tabadul_login_id",
                table: "merchant_configs");

            migrationBuilder.CreateTable(
                name: "cards",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    card_number = table.Column<string>(type: "text", nullable: false),
                    customer_id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    year_of_expiry = table.Column<string>(type: "text", nullable: false),
                    month_of_expiry = table.Column<string>(type: "text", nullable: false),
                    card_holder_name = table.Column<string>(type: "text", nullable: true),
                    is_active = table.Column<bool>(type: "boolean", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_cards", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_cards_card_number",
                table: "cards",
                column: "card_number",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "cards");

            migrationBuilder.AddColumn<string>(
                name: "merchant_tabadul_login_id",
                table: "merchant_configs",
                type: "character varying(26)",
                maxLength: 26,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "ix_merchant_configs_merchant_tabadul_login_id",
                table: "merchant_configs",
                column: "merchant_tabadul_login_id");

            migrationBuilder.AddForeignKey(
                name: "fk_merchant_configs_merchant_tabadul_logins_merchant_tabadul_l",
                table: "merchant_configs",
                column: "merchant_tabadul_login_id",
                principalTable: "merchant_tabadul_logins",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
