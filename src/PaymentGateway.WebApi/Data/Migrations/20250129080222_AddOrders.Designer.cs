// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using PaymentGateway.WebApi.Data;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    [DbContext(typeof(PaymentDbContext))]
    [Migration("20250129080222_AddOrders")]
    partial class AddOrders
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.Merchant", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_merchants");

                    b.ToTable("merchants", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<int>("MaxNotificationRetries")
                        .HasColumnType("integer")
                        .HasColumnName("max_notification_retries");

                    b.Property<string>("MerchantId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("merchant_id");

                    b.Property<bool>("PreventDuplicateCustomerOrders")
                        .HasColumnType("boolean")
                        .HasColumnName("prevent_duplicate_customer_orders");

                    b.Property<string>("PublicAPIUrl")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("public_api_url");

                    b.HasKey("Id")
                        .HasName("pk_merchant_configs");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_merchant_configs_merchant_id");

                    b.ToTable("merchant_configs", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantLogin", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("api_key");

                    b.Property<string>("MerchantId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("merchant_id");

                    b.Property<string>("MerchantTabadulLoginId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("merchant_tabadul_login_id");

                    b.HasKey("Id")
                        .HasName("pk_merchant_logins");

                    b.HasIndex("ApiKey")
                        .IsUnique()
                        .HasDatabaseName("ix_merchant_logins_api_key");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_merchant_logins_merchant_id");

                    b.HasIndex("MerchantTabadulLoginId")
                        .HasDatabaseName("ix_merchant_logins_merchant_tabadul_login_id");

                    b.ToTable("merchant_logins", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantTabadulLogin", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("TabadulPassword")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("tabadul_password");

                    b.Property<string>("TabadulUsername")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("tabadul_username");

                    b.HasKey("Id")
                        .HasName("pk_merchant_tabadul_logins");

                    b.HasIndex("TabadulUsername")
                        .IsUnique()
                        .HasDatabaseName("ix_merchant_tabadul_logins_tabadul_username");

                    b.ToTable("merchant_tabadul_logins", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Orders.Data.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<long>("Amount")
                        .HasColumnType("bigint")
                        .HasColumnName("amount");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("currency");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("customer_id");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("email");

                    b.Property<int>("ExpiresInSeconds")
                        .HasColumnType("integer")
                        .HasColumnName("expires_in_seconds");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("external_id");

                    b.Property<string>("MerchantId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("merchant_id");

                    b.Property<string>("Phone")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)")
                        .HasColumnName("phone");

                    b.HasKey("Id")
                        .HasName("pk_orders");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("ix_orders_customer_id");

                    b.HasIndex("ExternalId")
                        .IsUnique()
                        .HasDatabaseName("ix_orders_external_id");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_orders_merchant_id");

                    b.ToTable("orders", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Orders.Data.OrderEvent", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("description");

                    b.Property<string>("EventDetails")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("event_details");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("event_type");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text")
                        .HasColumnName("ip_address");

                    b.Property<Guid>("OrderId")
                        .HasMaxLength(26)
                        .HasColumnType("uuid")
                        .HasColumnName("order_id");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("order_status");

                    b.Property<string>("RequestId")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("request_id");

                    b.HasKey("Id")
                        .HasName("pk_order_events");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("ix_order_events_created_at");

                    b.HasIndex("EventType")
                        .HasDatabaseName("ix_order_events_event_type");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("ix_order_events_order_id");

                    b.HasIndex("OrderStatus")
                        .HasDatabaseName("ix_order_events_order_status");

                    b.HasIndex("RequestId")
                        .HasDatabaseName("ix_order_events_request_id");

                    b.ToTable("order_events", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Payments.Cards.Data.Card", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text")
                        .HasColumnName("card_holder_name");

                    b.Property<string>("CardNumber")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("card_number");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("customer_id");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<string>("MonthOfExpiry")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("month_of_expiry");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("YearOfExpiry")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("year_of_expiry");

                    b.HasKey("Id")
                        .HasName("pk_cards");

                    b.HasIndex("CustomerId", "CardNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_cards_customer_id_card_number");

                    b.ToTable("cards", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.TestFeature.Data.Test", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.HasKey("Id")
                        .HasName("pk_test");

                    b.ToTable("test", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantConfig", b =>
                {
                    b.HasOne("PaymentGateway.WebApi.Features.Merchants.Data.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_configs_merchants_merchant_id");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantLogin", b =>
                {
                    b.HasOne("PaymentGateway.WebApi.Features.Merchants.Data.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_logins_merchants_merchant_id");

                    b.HasOne("PaymentGateway.WebApi.Features.Merchants.Data.MerchantTabadulLogin", "MerchantTabadulLogin")
                        .WithMany()
                        .HasForeignKey("MerchantTabadulLoginId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_logins_merchant_tabadul_logins_merchant_tabadul_lo");

                    b.Navigation("Merchant");

                    b.Navigation("MerchantTabadulLogin");
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Orders.Data.OrderEvent", b =>
                {
                    b.HasOne("PaymentGateway.WebApi.Features.Orders.Data.Order", "Order")
                        .WithMany()
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_order_events_orders_order_id");

                    b.Navigation("Order");
                });
#pragma warning restore 612, 618
        }
    }
}
