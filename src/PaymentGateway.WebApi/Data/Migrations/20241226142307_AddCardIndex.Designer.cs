// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using PaymentGateway.WebApi.Data;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    [DbContext(typeof(PaymentDbContext))]
    [Migration("20241226142307_AddCardIndex")]
    partial class AddCardIndex
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.Merchant", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_merchants");

                    b.ToTable("merchants", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<int>("MaxNotificationRetries")
                        .HasColumnType("integer")
                        .HasColumnName("max_notification_retries");

                    b.Property<string>("MerchantId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("merchant_id");

                    b.Property<bool>("PreventDuplicateCustomerOrders")
                        .HasColumnType("boolean")
                        .HasColumnName("prevent_duplicate_customer_orders");

                    b.Property<string>("PublicAPIUrl")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("public_api_url");

                    b.HasKey("Id")
                        .HasName("pk_merchant_configs");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_merchant_configs_merchant_id");

                    b.ToTable("merchant_configs", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantLogin", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("ApiKey")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("api_key");

                    b.Property<string>("MerchantId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("merchant_id");

                    b.Property<string>("MerchantTabadulLoginId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("merchant_tabadul_login_id");

                    b.HasKey("Id")
                        .HasName("pk_merchant_logins");

                    b.HasIndex("ApiKey")
                        .IsUnique()
                        .HasDatabaseName("ix_merchant_logins_api_key");

                    b.HasIndex("MerchantId")
                        .HasDatabaseName("ix_merchant_logins_merchant_id");

                    b.HasIndex("MerchantTabadulLoginId")
                        .HasDatabaseName("ix_merchant_logins_merchant_tabadul_login_id");

                    b.ToTable("merchant_logins", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantTabadulLogin", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("TabadulPassword")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("tabadul_password");

                    b.Property<string>("TabadulUsername")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("tabadul_username");

                    b.HasKey("Id")
                        .HasName("pk_merchant_tabadul_logins");

                    b.HasIndex("TabadulUsername")
                        .IsUnique()
                        .HasDatabaseName("ix_merchant_tabadul_logins_tabadul_username");

                    b.ToTable("merchant_tabadul_logins", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Payments.Cards.Data.Card", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.Property<string>("CardHolderName")
                        .HasColumnType("text")
                        .HasColumnName("card_holder_name");

                    b.Property<string>("CardNumber")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("card_number");

                    b.Property<string>("CustomerId")
                        .IsRequired()
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("customer_id");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean")
                        .HasColumnName("is_active");

                    b.Property<string>("MonthOfExpiry")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("month_of_expiry");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<string>("YearOfExpiry")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("year_of_expiry");

                    b.HasKey("Id")
                        .HasName("pk_cards");

                    b.HasIndex("CustomerId", "CardNumber")
                        .IsUnique()
                        .HasDatabaseName("ix_cards_customer_id_card_number");

                    b.ToTable("cards", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.TestFeature.Data.Test", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(26)
                        .HasColumnType("character varying(26)")
                        .HasColumnName("id");

                    b.HasKey("Id")
                        .HasName("pk_test");

                    b.ToTable("test", (string)null);
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantConfig", b =>
                {
                    b.HasOne("PaymentGateway.WebApi.Features.Merchants.Data.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_configs_merchants_merchant_id");

                    b.Navigation("Merchant");
                });

            modelBuilder.Entity("PaymentGateway.WebApi.Features.Merchants.Data.MerchantLogin", b =>
                {
                    b.HasOne("PaymentGateway.WebApi.Features.Merchants.Data.Merchant", "Merchant")
                        .WithMany()
                        .HasForeignKey("MerchantId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_logins_merchants_merchant_id");

                    b.HasOne("PaymentGateway.WebApi.Features.Merchants.Data.MerchantTabadulLogin", "MerchantTabadulLogin")
                        .WithMany()
                        .HasForeignKey("MerchantTabadulLoginId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_merchant_logins_merchant_tabadul_logins_merchant_tabadul_lo");

                    b.Navigation("Merchant");

                    b.Navigation("MerchantTabadulLogin");
                });
#pragma warning restore 612, 618
        }
    }
}
