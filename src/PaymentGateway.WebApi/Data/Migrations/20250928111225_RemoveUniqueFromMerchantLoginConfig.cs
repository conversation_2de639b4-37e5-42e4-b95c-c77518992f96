using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class RemoveUniqueFromMerchantLoginConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_merchant_tabadul_logins_tabadul_username",
                table: "merchant_tabadul_logins");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "ix_merchant_tabadul_logins_tabadul_username",
                table: "merchant_tabadul_logins",
                column: "tabadul_username",
                unique: true);
        }
    }
}
