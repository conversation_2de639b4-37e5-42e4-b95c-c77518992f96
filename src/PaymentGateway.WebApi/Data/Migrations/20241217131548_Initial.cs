using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PaymentGateway.WebApi.Data.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "merchant_tabadul_logins",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    tabadul_username = table.Column<string>(type: "text", nullable: false),
                    tabadul_password = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_merchant_tabadul_logins", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "merchants",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_merchants", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "test",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_test", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "merchant_configs",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    merchant_id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    max_notification_retries = table.Column<int>(type: "integer", nullable: false),
                    prevent_duplicate_customer_orders = table.Column<bool>(type: "boolean", nullable: false),
                    merchant_tabadul_login_id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_merchant_configs", x => x.id);
                    table.ForeignKey(
                        name: "fk_merchant_configs_merchant_tabadul_logins_merchant_tabadul_l",
                        column: x => x.merchant_tabadul_login_id,
                        principalTable: "merchant_tabadul_logins",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_merchant_configs_merchants_merchant_id",
                        column: x => x.merchant_id,
                        principalTable: "merchants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "merchant_logins",
                columns: table => new
                {
                    id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    merchant_id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false),
                    api_key = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    merchant_tabadul_login_id = table.Column<string>(type: "character varying(26)", maxLength: 26, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_merchant_logins", x => x.id);
                    table.ForeignKey(
                        name: "fk_merchant_logins_merchant_tabadul_logins_merchant_tabadul_lo",
                        column: x => x.merchant_tabadul_login_id,
                        principalTable: "merchant_tabadul_logins",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_merchant_logins_merchants_merchant_id",
                        column: x => x.merchant_id,
                        principalTable: "merchants",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_merchant_configs_merchant_id",
                table: "merchant_configs",
                column: "merchant_id");

            migrationBuilder.CreateIndex(
                name: "ix_merchant_configs_merchant_tabadul_login_id",
                table: "merchant_configs",
                column: "merchant_tabadul_login_id");

            migrationBuilder.CreateIndex(
                name: "ix_merchant_logins_api_key",
                table: "merchant_logins",
                column: "api_key",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_merchant_logins_merchant_id",
                table: "merchant_logins",
                column: "merchant_id");

            migrationBuilder.CreateIndex(
                name: "ix_merchant_logins_merchant_tabadul_login_id",
                table: "merchant_logins",
                column: "merchant_tabadul_login_id");

            migrationBuilder.CreateIndex(
                name: "ix_merchant_tabadul_logins_tabadul_username",
                table: "merchant_tabadul_logins",
                column: "tabadul_username",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "merchant_configs");

            migrationBuilder.DropTable(
                name: "merchant_logins");

            migrationBuilder.DropTable(
                name: "test");

            migrationBuilder.DropTable(
                name: "merchant_tabadul_logins");

            migrationBuilder.DropTable(
                name: "merchants");
        }
    }
}
