using FastEndpoints;
using FastEndpoints.Swagger;
using Hangfire;
using Hangfire.Dashboard;
using PaymentGateway.Integrations.Extensions;
using PaymentGateway.WebApi.Auth.Extensions;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Extensions;
using PaymentGateway.WebApi.Common.Middlewares;
using PaymentGateway.WebApi.Features.Merchants.Extensions;
using PaymentGateway.WebApi.Features.Orders.Extensions;
using Serilog;

var builder = WebApplication.CreateBuilder(args);
builder.AddCommon()
    .AddAuth()
    .AddOrders()
    .AddMerchants()
    .Services
    .AddTabadulClient(builder.Configuration);

var app = builder.Build();
app.UseMiddleware<RequestIdMiddleware>()
    .UseMiddleware<GlobalExceptionHandlerMiddleware>()
    .UseAuthentication()
    .UseAuthorization()
    .UseFastEndpoints(c =>
    {
        c.Errors.ResponseBuilder = (failures, _, statusCode) =>
        {
            var problemDetails = new ValidationProblemDetails(ErrorNumbers.ValidationError, AppConstants.ValidationErrorTitle, statusCode, failures
                .Select(m => new ValidationError(m.PropertyName, m.ErrorCode, m.ErrorMessage.ToString()))
                .ToArray());

            return problemDetails;
        };
    })
    .UseSwaggerGen()
    .UseHangfireDashboard("/hangfire", new DashboardOptions
    {
        Authorization = [app.Services.GetRequiredService<IDashboardAuthorizationFilter>(),],
    });
await app.MigrateAsync();

try { app.Run(); }
catch (Exception ex) { Log.Fatal(ex, "Payment Gateway API startup failed!"); }
finally { Log.CloseAndFlush(); }
