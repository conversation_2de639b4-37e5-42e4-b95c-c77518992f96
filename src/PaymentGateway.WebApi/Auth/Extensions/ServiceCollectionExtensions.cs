using Microsoft.AspNetCore.Authentication;
using PaymentGateway.WebApi.Auth.Services;
using PaymentGateway.WebApi.Common;
using PaymentGateway.WebApi.Common.Middlewares;

namespace PaymentGateway.WebApi.Auth.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAuthServices(this IServiceCollection services)
    {
        services.AddScoped<IClaimsExtractor, ClaimsExtractor>();
        services.AddScoped<IJwsCreator, JwsCreator>();
        services.AddAuthentication()
            .AddScheme<AuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(AppConstants.SecuritySchemes.ApiKeySchemeName, _ =>
            {
                
            })
            .AddScheme<AuthenticationSchemeOptions, BasicAuthenticationHandler>(AppConstants.SecuritySchemes.BasicSchemeName, _ =>
            {
                
            });

        services.AddAuthorizationBuilder();
        return services;
    }
}
