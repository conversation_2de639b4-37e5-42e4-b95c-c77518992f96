using PaymentGateway.WebApi.Common;

namespace PaymentGateway.WebApi.Auth.Shared
{
    public abstract class AuthenticationError
    {
        public static Error ApiKeyMissing =>
            Error.Create(ErrorNumbers.AuthErrors.ApiKeyMissing,nameof(ApiKeyMissing), "API key is missing", StatusCodes.Status401Unauthorized);

        public static Error ApiKeyInvalid =>
            Error.Create(ErrorNumbers.AuthErrors.ApiKeyInvalid,nameof(ApiKeyInvalid), "API key is invalid", StatusCodes.Status401Unauthorized);

    }
}
