using PaymentGateway.WebApi.Features.Merchants.Data;

namespace PaymentGateway.WebApi.Auth.Services;

public interface IClaimsExtractor
{
    string? GetMerchantIdAsString();
    MerchantId GetMerchantId();
}
public class ClaimsExtractor(IHttpContextAccessor httpContextAccessor) : IClaimsExtractor
{
    public string? GetMerchantIdAsString()
    {
        return httpContextAccessor.HttpContext?.User.FindFirst("MerchantId")?.Value;
    }
    
    public MerchantId GetMerchantId()
    {
        var merchantId = GetMerchantIdAsString();
        if (merchantId == null)
        {
            throw new InvalidOperationException("MerchantId claim is not set");
        }
        return MerchantId.From(merchantId);
    }
}
