using System.IdentityModel.Tokens.Jwt;
using System.Security.Cryptography;
using Microsoft.IdentityModel.Tokens;
using PaymentGateway.Integrations.Extensions;
using PaymentGateway.WebApi.Auth.Configurations;
using PaymentGateway.WebApi.Features.Merchants.Data;
using PaymentGateway.WebApi.Features.Orders.Data;

namespace PaymentGateway.WebApi.Auth.Services;

public sealed record MerchantNotificationPayload(Guid OrderId, ExternalId ExternalId, MerchantId MerchantId, string MerchantName, List<StatusChange> StatusChanges, int RetryCount);

public sealed record StatusChange(Guid OrderId, ExternalId OrderExternalId, OrderStatuses Status, DateTimeOffset Timestamp);

public interface IJwsCreator
{
    string CreateSignedJws(MerchantNotificationPayload payload);
}

public sealed class JwsCreator : IJwsCreator, IDisposable
{
    private readonly RSA _rsa;
    private readonly RsaSecurityKey _rsaSecurityKey;
    private bool _disposed;

    public JwsCreator(AuthConfiguration authConfiguration)
    {
        _rsa = RSA.Create();
        _rsa.ImportFromPem(authConfiguration.RsaPrivateKey.ToCharArray());

        _rsaSecurityKey = new RsaSecurityKey(_rsa)
        {
            CryptoProviderFactory = new CryptoProviderFactory
            {
                CacheSignatureProviders = true,
            },
        };
    }

    public string CreateSignedJws(MerchantNotificationPayload payload)
    {
        ThrowIfDisposed();

        var payloadDictionary = CreatePayloadDictionary(payload);
        var credentials = new SigningCredentials(_rsaSecurityKey, SecurityAlgorithms.RsaSha256);

        var header = new JwtHeader(credentials);
        var jwtPayload = new JwtPayload();

        foreach (var kvp in payloadDictionary) jwtPayload.Add(kvp.Key, kvp.Value);


        var token = new JwtSecurityToken(header, jwtPayload);
        var jws = new JwtSecurityTokenHandler().WriteToken(token);
        return jws;
    }

    private static Dictionary<string, object> CreatePayloadDictionary(MerchantNotificationPayload payload)
    {
        var payloadDictionary = new Dictionary<string, object>
        {
            [nameof(payload.OrderId)
                .ToCamelCase()] = payload.OrderId.ToString(),
            [nameof(payload.MerchantId)
                .ToCamelCase()] = payload.MerchantId.Value,
            [nameof(payload.MerchantName)
                .ToCamelCase()] = payload.MerchantName,
            [nameof(payload.RetryCount)
                .ToCamelCase()] = payload.RetryCount.ToString(),
            [nameof(payload.ExternalId)
                .ToCamelCase()] = payload.ExternalId.Value,
        };

        var statusChangesList = payload.StatusChanges
            .Select(sc => new Dictionary<string, object>
            {
                [nameof(StatusChange.OrderId)
                    .ToCamelCase()] = sc.OrderId.ToString(),
                [nameof(StatusChange.OrderExternalId)
                    .ToCamelCase()] = sc.OrderExternalId.Value,
                [nameof(StatusChange.Status)
                    .ToCamelCase()] = sc.Status.ToString().ToCamelCase(),
                [nameof(StatusChange.Timestamp)
                    .ToCamelCase()] = sc.Timestamp.ToString("o")
            })
            .ToList();

        payloadDictionary[nameof(payload.StatusChanges)
            .ToCamelCase()] = statusChangesList;
        return payloadDictionary;
    }

    public void Dispose()
    {
        if (_disposed) return;
        _rsa.Dispose();
        _disposed = true;
    }

    private void ThrowIfDisposed()
    {
        if (!_disposed) return;
        throw new ObjectDisposedException(nameof(JwsCreator));
    }
}
